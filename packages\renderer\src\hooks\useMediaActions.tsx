// hooks/useMediaActions.ts
import React, { useMemo } from 'react'
import { Edit, Trash, FolderInput } from 'lucide-react'
import { ResourceSource } from '@/types/resources'
import { MediaAction } from '@/pages/Projects/material/components/MediaItem'
import { useItemActions } from '@/hooks/useItemActions'

export const useMediaActions = (
  isLocal: boolean,
  type: ResourceSource,
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  const { renameItem, deleteItem, deleteLocalItem } = useItemActions()

  return useMemo<MediaAction[]>(
    () => [
      {
        icon: <Edit className="w-4 h-4" />,
        label: '修改',
        onClick: (fileId, fileName) =>
          renameItem(ResourceSource.MEDIA, fileId, fileName, {
            label: '素材名称',
            headerTitle: '素材',
          }),
      },
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: '移动到',
        onClick: fileId => {
          setMoveType(type)
          setMoveId(fileId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: '删除',
        onClick: (fileId, fileName) => {
          if (isLocal) {
            return deleteLocalItem(type, fileId, fileName!)
          }
          return deleteItem(type, fileId, fileName!)
        },
      },
    ],
    [renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen],
  )
}
