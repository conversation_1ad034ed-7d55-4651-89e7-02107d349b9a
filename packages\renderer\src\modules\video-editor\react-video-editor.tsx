import React from 'react'

import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'

import {
  AssetLoadingProvider,
  CachedOverlaysProvider,
  EditorProvider,
  SidebarProvider as EditorSidebarProvider,
  TimelineProvider
} from './contexts'
import { EditorDndWrapper } from './components/editor-dnd-wrapper'
import { EditorLayout } from './layout'

export default React.memo(
  function ReactVideoEditor({ scriptId, projectId }: { scriptId: string, projectId: string }) {
    return (
      <EditorSidebarProvider>
        <EditorProvider scriptId={scriptId} projectId={projectId}>
          <TimelineProvider>
            <CachedOverlaysProvider>
              <AssetLoadingProvider>
                <EditorDndWrapper>
                  <div className="flex flex-col size-full">
                    <EditorLayout.Header />

                    <div className="flex-1 w-full flex">
                      <EditorLayout.Materials.Sidebar />

                      <ResizablePanelGroup direction="vertical" className="h-full flex-1">
                        <ResizablePanel defaultSize={50} minSize={50}>
                          <ResizablePanelGroup direction="horizontal">
                            <ResizablePanel defaultSize={24} minSize={15} maxSize={30}>
                              <EditorLayout.Materials.Content />
                            </ResizablePanel>
                            <ResizableHandle />

                            <ResizablePanel defaultSize={60} minSize={40}>
                              <EditorLayout.Player />
                            </ResizablePanel>
                            <ResizableHandle />

                            <ResizablePanel defaultSize={22} minSize={15} maxSize={30}>
                              <EditorLayout.Operation />
                            </ResizablePanel>
                          </ResizablePanelGroup>
                        </ResizablePanel>
                        <ResizableHandle />

                        <ResizablePanel defaultSize={50} minSize={5}>
                          <div className="h-full overflow-y-hidden flex flex-col">
                            <EditorLayout.Toolbar />
                            <EditorLayout.Timeline />
                          </div>
                        </ResizablePanel>
                      </ResizablePanelGroup>
                    </div>
                  </div>
                </EditorDndWrapper>
              </AssetLoadingProvider>
            </CachedOverlaysProvider>
          </TimelineProvider>
        </EditorProvider>
      </EditorSidebarProvider>
    )
  }
)
