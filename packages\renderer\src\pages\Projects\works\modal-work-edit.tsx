import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from '@/components/modal'
import { genForm } from '@/libs/tools/form'
import z from 'zod'
import { useModal, useModalContext } from '@/libs/tools/modal'
import { Work } from '@/types/project'
import { usePending } from '@/hooks/usePending'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'

const WorkEditForm = genForm(
  z.object({
    title: z.string().min(1, '标题不能为空'),
  }),
  {
    fields: {
      title: {
        label: '作品标题',
      },
    },
  },
)

function ModalWorkEdit({ work }: { work: Work }) {
  const queryClient = useQueryClient()
  const { close } = useModalContext()
  const { pending, withPending } = usePending()

  return (
    <>
      <ModalHeader title="编辑作品" />
      <WorkEditForm
        defaultValues={{ title: work.name }}
        onSubmit={withPending(async ({ title }) => {
          await ResourceModule.work.rename({
            id: work.scriptId,
            name: title,
          })
          await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.WORK_LIST] })
          close()
        })}
      >
        <ModalFooter pending={pending} />
      </WorkEditForm>
    </>
  )
}

export const useModalWorkEdit = () => {
  const modal = useModal()

  return (work: Work) =>
    modal({
      content: <ModalWorkEdit work={work} />,
    })
}
