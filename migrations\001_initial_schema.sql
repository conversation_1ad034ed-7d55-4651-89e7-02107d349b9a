
-- 创建文件夹表
CREATE TABLE folder (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键，自增
    parent_id INTEGER DEFAULT 0, -- 父级文件夹id
    deleted_at INT(13) DEFAULT 0, -- 是否删除 0 未删除
    material_type INT(2) DEFAULT 2, -- 文件为1 文件夹为2
    path varchar(255) DEFAULT '', -- 文件夹路径
    title varchar(255) DEFAULT '', -- 文件夹名
    team_id INT(8), -- 团队id
    uid varchar(32) DEFAULT '', -- 用户uid
    updated_at INT(13), -- 更新时间戳
    auto_create INT(2) DEFAULT 1, -- 1手动创建 2选择文件夹创建系统目录
    created_at INT(13) -- 创建时间戳
);

-- 创建文件夹索引
CREATE INDEX idx_folder_parent_id ON folder(parent_id);
CREATE INDEX idx_folder_uid ON folder(uid);
CREATE INDEX idx_folder_team_id ON folder(team_id);
CREATE INDEX idx_folder_deleted_at ON folder(deleted_at);

-- 创建素材文件表
CREATE TABLE material_file (
    id varchar(40) PRIMARY KEY NOT NULL, -- 文件唯一ID
    parent_id INTEGER DEFAULT 0, -- 所属文件夹ID
    team_id INT(8), -- 团队ID
    uid varchar(32) DEFAULT '', -- 用户UID
    material_type INT(2) DEFAULT 1, -- 素材类型
    path varchar(255) DEFAULT '', -- 本地文件路径
    source_path varchar(255) DEFAULT '', -- 原始文件路径
    size INT(10) DEFAULT 0, -- 文件大小（字节）
    duration BIGINT DEFAULT 0, -- 文件时长（毫秒）
    width INT(4) DEFAULT 0, -- 像素宽度
    height INT(4) DEFAULT 0, -- 像素高度
    codec_name varchar(30) DEFAULT '', -- 编码器名称
    codec_type varchar(30) DEFAULT '', -- 编码类型（audio/video）
    bit_rate INT(30), -- 码率
    nb_frames INT(30), -- 总帧数
    cover varchar(255) DEFAULT '', -- 封面图路径
    hash varchar(40) DEFAULT '', -- 文件哈希
    status INT(2) DEFAULT 0, -- 状态：0未上传，1处理中...
    clip_cloud_or_local INT(2) DEFAULT 0, -- 存储位置：0初始，1云端，2本地
    object_oid varchar(32) DEFAULT '', -- 对象ID
    reason text DEFAULT '', -- 失败原因
    title varchar(255) DEFAULT '', -- 文件名
    upload_id INT(13) DEFAULT 0, -- 上传ID
    task_no varchar(32), -- 关联任务编号
    clipinfo text DEFAULT '', -- 裁剪信息(JSON)
    updated_at INT(13), -- 更新时间戳
    deleted_at INT(13) DEFAULT 0, -- 删除时间戳
    created_at INT(13), -- 创建时间戳
    tag_id int(10) default 0 -- 标签ID
);

-- 创建素材文件索引
CREATE INDEX idx_material_file_parent_id ON material_file(parent_id);
CREATE INDEX idx_material_file_uid ON material_file(uid);
CREATE INDEX idx_material_file_team_id ON material_file(team_id);
CREATE INDEX idx_material_file_material_type ON material_file(material_type);
CREATE INDEX idx_material_file_hash ON material_file(hash);
CREATE INDEX idx_material_file_deleted_at ON material_file(deleted_at);

-- 创建任务表
CREATE TABLE task (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键
    uid varchar(32) DEFAULT '', -- 用户UID
    team_id INT(8) DEFAULT 0, -- 团队ID
    group_id INT(10) DEFAULT 0, -- 分组ID
    make_cover_frame INT(2) DEFAULT 0, -- 是否生成封面帧
    make_track_frame INT(2) DEFAULT 0, -- 是否生成轨道帧
    need_bind INT(2) DEFAULT 0, -- 是否需要绑定
    path varchar(255) DEFAULT '', -- 本地文件路径
    url varchar(255) DEFAULT '', -- 文件URL
    script_id INT(10) DEFAULT 0, -- 脚本ID
    resource_id varchar(50) DEFAULT '', -- 资源ID
    cover varchar(255) DEFAULT '', -- 封面图
    cover_frame varchar(255) DEFAULT '', -- 封面帧
    track_frame varchar(255) DEFAULT '', -- 轨道帧
    name varchar(255) DEFAULT '', -- 任务名称
    codec_name varchar(30) DEFAULT '', -- 编码器名称
    hash varchar(40) DEFAULT '', -- 文件哈希
    duration INT(10) DEFAULT 0, -- 时长
    width INT(4) DEFAULT 0, -- 宽
    height INT(4) DEFAULT 0, -- 高
    size INT(10) DEFAULT 0, -- 大小
    status INT(2) DEFAULT 0, -- 状态
    progress INT(3) DEFAULT 0, -- 进度
    folder_id varchar(40) DEFAULT '', -- 文件夹ID
    upload_id varchar(40) DEFAULT '', -- 上传ID
    type INT(2) DEFAULT 0, -- 任务类型
    reason varchar(255) DEFAULT '', -- 失败原因
    task_nos varchar(255) DEFAULT '', -- 关联任务编号
    extra_tasks text DEFAULT '', -- 额外任务信息
    upload_type varchar(20) DEFAULT '', -- 上传类型
    tag_id INT(10) DEFAULT 0, -- 标签ID
    object_key varchar(255) DEFAULT '', -- 对象键
    platform INT(2) DEFAULT 0, -- 平台
    deleted_at INT(13) DEFAULT 0, -- 删除时间戳
    updated_at INT(13), -- 更新时间戳
    created_at INT(13) -- 创建时间戳
);

-- 创建上传任务表
CREATE TABLE upload_task (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键
    uid varchar(32) DEFAULT '', -- 用户UID
    team_id INT(8) DEFAULT 0, -- 团队ID
    name varchar(255) DEFAULT '', -- 文件名称
    local_path varchar(500) DEFAULT '', -- 本地文件路径
    url varchar(500) DEFAULT '', -- 云端URL
    hash varchar(64) DEFAULT '', -- 文件哈希值
    size BIGINT DEFAULT 0, -- 文件大小（字节）
    uploaded_bytes BIGINT DEFAULT 0, -- 已上传字节数
    progress INT(3) DEFAULT 0, -- 上传进度（0-100）
    status INT(2) DEFAULT 0, -- 任务状态（0:等待 1:上传中 2:暂停 3:完成 4:失败 5:取消）
    type INT(2) DEFAULT 4, -- 文件类型（1:视频 2:音频 3:图片 4:其他）
    reason varchar(500) DEFAULT '', -- 失败原因
    folder_id varchar(40) DEFAULT '', -- 所属文件夹ID
    object_key varchar(500) DEFAULT '', -- OSS对象键
    object_id varchar(100) DEFAULT '', -- OSS对象ID
    upload_module varchar(20) DEFAULT 'media', -- 上传模块类型
    checkpoint_data TEXT DEFAULT '', -- OSS断点续传数据（JSON格式）
    deleted_at INT(13) DEFAULT 0, -- 删除时间戳
    updated_at INT(13), -- 更新时间戳
    created_at INT(13) -- 创建时间戳
);

-- 创建上传任务索引
CREATE INDEX idx_upload_task_uid ON upload_task(uid);
CREATE INDEX idx_upload_task_team_id ON upload_task(team_id);
CREATE INDEX idx_upload_task_status ON upload_task(status);
CREATE INDEX idx_upload_task_folder_id ON upload_task(folder_id);
CREATE INDEX idx_upload_task_created_at ON upload_task(created_at);
CREATE INDEX idx_upload_task_deleted_at ON upload_task(deleted_at);

-- 创建任务索引
CREATE INDEX idx_task_uid ON task(uid);
CREATE INDEX idx_task_team_id ON task(team_id);
CREATE INDEX idx_task_status ON task(status);
CREATE INDEX idx_task_type ON task(type);
CREATE INDEX idx_task_folder_id ON task(folder_id);
CREATE INDEX idx_task_deleted_at ON task(deleted_at);
CREATE INDEX idx_task_platform ON task(platform);

-- 创建合成记录表
CREATE TABLE compose (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键
    cid INT(10) DEFAULT 0, -- 合成任务ID
    team_id INT(5) DEFAULT 0, -- 团队ID
    script_id varchar(30) DEFAULT '', -- 脚本ID
    url varchar(30) DEFAULT '', -- 合成视频URL
    path varchar(255) DEFAULT '', -- 本地下载路径
    object_oid varchar(30) DEFAULT '', -- 合成文件对象ID
    name varchar(255) DEFAULT '', -- 文件名
    cover varchar(255) DEFAULT '', -- 云端封面图
    duration INT(10) DEFAULT 0, -- 时长
    width INT(4) DEFAULT 0, -- 宽
    height INT(4) DEFAULT 0, -- 高
    size INT(10) DEFAULT 0, -- 大小
    status INT(2) DEFAULT 0, -- 状态
    reason varchar(255) DEFAULT '', -- 失败原因
    download_count INT(10) DEFAULT 0, -- 下载次数
    download_progress INT(3) DEFAULT 0, -- 下载进度
    download_status INT(2) DEFAULT 0, -- 下载状态
    download_reason varchar(255) DEFAULT '', -- 下载失败原因
    read INT(2) DEFAULT 0, -- 是否已读
    notify INT(2) DEFAULT 0, -- 是否通知
    auto_download INT(1) DEFAULT 0, -- 自动下载
    auto_download_path varchar(255) DEFAULT '', -- 自动下载路径
    group_At int(10), -- 分组时间
    download_at int(10), -- 下载完成时间
    deleted_at int(10) DEFAULT 0, -- 删除时间
    updated_at int(10), -- 更新时间
    created_at int(10) -- 创建时间
);

-- 创建合成记录表索引
CREATE INDEX idx_compose_cid ON compose(cid);
CREATE INDEX idx_compose_team_id ON compose(team_id);
CREATE INDEX idx_compose_status ON compose(status);
CREATE INDEX idx_compose_deleted_at ON compose(deleted_at);
CREATE INDEX idx_compose_script_id ON compose(script_id);
