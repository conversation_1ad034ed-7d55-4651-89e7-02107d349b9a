import { useQuery } from '@tanstack/react-query'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { cacheManager } from '@/libs/cache/cache-manager'

export interface ResourceCacheStatusResult {
  isCached: boolean
  isChecking: boolean
  refreshCacheStatus: () => void
}

export function useResourceCacheStatus(
  resourceType?: ResourceType,
  resourceUrl?: string,
): ResourceCacheStatusResult {
  const query = useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl],
    queryFn: () => {
      if (!resourceType || !resourceUrl) {
        return false
      }

      return cacheManager.resource.isCachedSync(resourceType, resourceUrl)
    },
    enabled: !!(resourceType && resourceUrl),
    staleTime: 0,
  })

  return {
    isCached: query.data ?? false,
    isChecking: query.isLoading || query.isFetching,
    refreshCacheStatus: query.refetch
  }
}

