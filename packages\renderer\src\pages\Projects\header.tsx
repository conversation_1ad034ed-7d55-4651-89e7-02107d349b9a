import { cn } from '@/components/lib/utils'
import { Input } from '@/components/ui/input'
import React, { useMemo, useState } from 'react'
import { NavLink, useSearchParams } from 'react-router'
import { Search } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { useProjectCollaboratorModal } from './modal-collaborator'
import { useProjectsContext } from './context'
import { useQueryTeamMemberByID } from '@/hooks/queries/useQueryTeam'
import { TokenManager } from '@/libs/storage'
import { debounce } from 'lodash'

type Route = {
  path: string
  title: string
}

const paths: Route[] = [
  { path: 'creative', title: '我的创意' },
  { path: 'material', title: '我的素材' },
  { path: 'works', title: '我的作品' },
  { path: 'recycle-bin', title: '回收站' },
]

export function Header() {
  const openCollaboratorModal = useProjectCollaboratorModal()
  const [searchParams, setSearchParams] = useSearchParams()
  const [keywork, setKeywork] = useState(searchParams.get('keyword') || '')
  const { currentProject } = useProjectsContext()
  const { data: currentMember } = useQueryTeamMemberByID(TokenManager.getUserId())

  const updateKeyworkSearchParams = useMemo(
    () =>
      debounce((value: string) => {
        setSearchParams(prev => (prev.set('keyword', value), prev))
      }, 200),
    [],
  )

  const updateKeyword = (value: string) => {
    setKeywork(value)
    updateKeyworkSearchParams(value)
  }

  return (
    <div className="pt-2 flex px-4 justify-between items-center whitespace-nowrap">
      <div className="flex bg-background/50 p-1 gap-0 text-sm xl:text-base xl:gap-4 rounded-lg inset-shadow-muted-foreground">
        {paths.map(({ path, title }) => (
          <NavLink
            key={path}
            to={path}
            className={({ isActive }) => {
              return cn(
                'text-secondary-foreground px-1.5 xl:px-4 py-1 rounded-md',
                isActive && 'bg-linear-to-t dark:bg-linear-to-b from-muted-foreground/70 to-muted-foreground/40',
              )
            }}
          >
            {title}
          </NavLink>
        ))}
      </div>
      <div className="flex items-center gap-2">
        {currentMember?.roles?.some(role => ['owner', 'admin', 'manager'].includes(role.code)) && (
          <Button
            className="text-sm"
            variant="outline"
            disabled={!currentProject}
            onClick={() => openCollaboratorModal(currentProject!)}
          >
            添加协作者
          </Button>
        )}
        <div className="relative">
          <Input
            placeholder="请输入关键词搜索"
            value={keywork}
            onChange={e => updateKeyword(e.target.value)}
            className="pr-10"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
        </div>
      </div>
    </div>
  )
}
