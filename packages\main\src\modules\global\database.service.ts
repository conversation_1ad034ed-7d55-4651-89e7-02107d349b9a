import { Injectable } from '@nestjs/common'
import Database from 'better-sqlite3'
import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

/**
 * 数据库错误类
 */
export class DatabaseError extends Error {

  constructor(message: string, public readonly cause?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}

/**
 * 数据库服务
 * 负责管理SQLite数据库连接和基本操作
 */
@Injectable()
export class NestDatabaseService {

  /**
     * SQLite数据库实例
     */
  private _db: Database.Database | null = null

  /**
     * 数据库文件路径
     */
  private readonly _dbPath: string

  /**
     * 构造函数
     * @param dbName 数据库名称
     */
  constructor(dbName: string) {
    if (!dbName) {
      throw new DatabaseError('数据库名称不能为空')
    }

    // 设置数据库文件路径（存储在应用数据目录）
    this._dbPath = path.join(app.getPath('userData'), dbName)
  }

  /**
     * 获取数据库实例
     */
  get db(): Database.Database {
    if (!this._db) {
      throw new DatabaseError('数据库尚未初始化，请先调用 init() 方法')
    }
    return this._db
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    if (this._db) {
      console.log('数据库已经初始化')
      return
    }

    try {
      // 确保数据库目录存在
      const dbDir = path.dirname(this._dbPath)
      await fs.promises.mkdir(dbDir, { recursive: true })

      // 创建数据库连接
      this._db = new Database(this._dbPath, {
        verbose: undefined,
      })

      // 启用外键约束
      this._db.pragma('foreign_keys = ON')

      // 执行初始化
      this.executeBuiltinInitialSchema()

      console.log(`数据库服务初始化成功: ${this._dbPath}`)
    }
    catch (error) {
      console.error('数据库初始化失败:', error)
      throw new DatabaseError('数据库初始化失败', error)
    }
  }

  /**
   * 执行内置的初始化Schema
   */
  private executeBuiltinInitialSchema(): void {
    if (!this._db) {
      throw new DatabaseError('数据库尚未初始化')
    }

    const initialSchemaSQL = this.getInitialSchemaSQL()

    try {
      // 执行初始化SQL
      this._db.exec(initialSchemaSQL)
      console.log('已执行内置初始化Schema')
    } catch (error) {
      console.error('执行内置初始化Schema失败:', error)
      throw new DatabaseError('执行内置初始化Schema失败', error)
    }
  }

  /**
   * 获取初始化Schema SQL
   */
  private getInitialSchemaSQL(): string {
    return `
-- 创建上传任务表
CREATE TABLE IF NOT EXISTS upload_task (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键
    uid varchar(32) DEFAULT '', -- 用户UID
    team_id INT(8) DEFAULT 0, -- 团队ID
    name varchar(255) DEFAULT '', -- 文件名称
    local_path varchar(500) DEFAULT '', -- 本地文件路径
    url varchar(500) DEFAULT '', -- 云端URL
    hash varchar(64) DEFAULT '', -- 文件哈希值
    size BIGINT DEFAULT 0, -- 文件大小（字节）
    progress INT(3) DEFAULT 0, -- 上传进度（0-100）
    status INT(2) DEFAULT 0, -- 任务状态（0:等待 1:上传中 2:暂停 3:完成 4:失败 5:取消）
    type INT(2) DEFAULT 4, -- 文件类型（1:视频 2:音频 3:图片 4:其他）
    reason varchar(500) DEFAULT '', -- 失败原因
    folder_id varchar(40) DEFAULT '', -- 所属文件夹ID
    object_key varchar(500) DEFAULT '', -- OSS对象键
    object_id varchar(100) DEFAULT '', -- OSS对象ID
    upload_module varchar(50) DEFAULT 'oss', -- 上传模块
    checkpoint_data TEXT DEFAULT '', -- 断点续传数据
    created_at INT(13), -- 创建时间戳
    updated_at INT(13) -- 更新时间戳
    deleted_at INT(13) DEFAULT 0, -- 删除时间戳
);

-- 创建上传任务表索引
CREATE INDEX IF NOT EXISTS idx_upload_task_uid ON upload_task(uid);
CREATE INDEX IF NOT EXISTS idx_upload_task_team_id ON upload_task(team_id);
CREATE INDEX IF NOT EXISTS idx_upload_task_status ON upload_task(status);
CREATE INDEX IF NOT EXISTS idx_upload_task_hash ON upload_task(hash);
`
  }

  /**
     * 关闭数据库连接
     */
  protected async onCleanup(): Promise<void> {
    if (this._db) {
      this._db.close()
      this._db = null
      console.log('数据库连接已关闭')
    }
  }

  /**
     * 健康检查
     */
  protected async onHealthCheck(): Promise<{
    healthy: boolean
    status: string
    details?: Record<string, any>
  }> {
    try {
      if (!this._db) {
        return {
          healthy: false,
          status: '数据库未初始化',
        }
      }

      // 执行简单查询测试连接
      this._db.prepare('SELECT 1').get()

      return {
        healthy: true,
        status: '数据库连接正常',
        details: {
          path: this._dbPath,
        },
      }
    }
    catch (error) {
      return {
        healthy: false,
        status: '数据库连接异常',
        details: {
          error: error instanceof Error ? error.message : String(error),
        },
      }
    }
  }
}