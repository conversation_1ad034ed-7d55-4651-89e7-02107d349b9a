import React, { useState } from 'react'
import { cn } from '@/components/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { GalleryHorizontal, GalleryVertical, List } from 'lucide-react'
import { MaterialResource } from '@/types/resources'
import { Button } from '@/components/ui/button'
import { SearchInput } from '@/components/ui/search-input'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'

const durationOptions = [
  { value: [0, 15000], label: '0-15s' },
  { value: [15000, 30000], label: '15-30s' },
  { value: [30000, 60000], label: '30-60s' },
  { value: [60000, 18000000], label: '60s以上' }, //60s到5小时
]

interface FilterProps {
  filters: MaterialResource.MaterialMediaParams
  orientation: MaterialResource.MediaStyle
  setFilters: React.Dispatch<React.SetStateAction<any>>
  setOrientation: React.Dispatch<React.SetStateAction<any>>
}

interface SortMenuProps {
  sortField: MaterialResource.SortField
  sortOrder: MaterialResource.SortOrder
  onChange: (field: MaterialResource.SortField, order: MaterialResource.SortOrder) => void
  size?: 'sm' | 'default'
}
export const sortOptions: MaterialResource.SortOption[] = [
  {
    label: '上传时间',
    field: MaterialResource.SortField.UPLOAD_TIME,
    directions: [
      { label: '近-远', order: MaterialResource.SortOrder.ASC },
      { label: '远-近', order: MaterialResource.SortOrder.DESC },
    ],
  },
  {
    label: '文件大小',
    field: MaterialResource.SortField.FILE_SIZE,
    directions: [
      { label: '小-大', order: MaterialResource.SortOrder.ASC },
      { label: '大-小', order: MaterialResource.SortOrder.DESC },
    ],
  },
  {
    label: '媒体时长',
    field: MaterialResource.SortField.DURATION,
    directions: [
      { label: '短-长', order: MaterialResource.SortOrder.ASC },
      { label: '长-短', order: MaterialResource.SortOrder.DESC },
    ],
  },
  {
    label: '名称',
    field: MaterialResource.SortField.FILE_NAME,
    directions: [
      { label: 'A-Z', order: MaterialResource.SortOrder.ASC },
      { label: 'Z-A', order: MaterialResource.SortOrder.DESC },
    ],
  },
  {
    label: '引用次数',
    field: MaterialResource.SortField.QUOTE_COUNT,
    directions: [
      { label: '低-高', order: MaterialResource.SortOrder.ASC },
      { label: '高-低', order: MaterialResource.SortOrder.DESC },
    ],
  },
]
export const SortMenu: React.FC<SortMenuProps> = ({ sortField, sortOrder, onChange, size = 'default' }) => {
  const [menuOpen, setMenuOpen] = useState(false)
  const [activeField, setActiveField] = useState<MaterialResource.SortField>(sortField ?? sortOptions[0].field)
  const [SortOrder, setSortOrder] = useState<MaterialResource.SortOrder>(
    sortOrder ?? sortOptions[0].directions[0].order,
  )

  const menuSizeClasses = {
    sm: 'text-xs min-w-[80px]',
    default: 'text-sm min-w-[100px] bg-transparent',
  }
  const buttonSizeClasses = {
    sm: 'flex-row-reverse justify-between bg-primary/10 border-0',
    default: 'flex-row bg-white/5 py-4.5',
  }
  return (
    <div className="relative inline-block">
      <Button
        variant="outline"
        {...(size ? { size } : {})}
        className={cn('flex items-center gap-1', buttonSizeClasses[size])}
        onClick={() => {
          setMenuOpen(prev => {
            const next = !prev
            if (next) setActiveField(sortOptions[0].field)
            return next
          })
        }}
      >
        <List className="inline-block w-4 h-4" />
        排序
      </Button>

      {menuOpen && (
        <div
          className={cn(
            'absolute top-full mt-2 flex flex-col bg-white dark:bg-neutral-800 border shadow-md rounded z-20',
            size === 'sm' && 'right-0',
            menuSizeClasses[size],
          )}
          onMouseEnter={() => setMenuOpen(true)}
          onMouseLeave={() => setMenuOpen(false)}
        >
          {/* 一级菜单 */}
          <div className="flex flex-col border-b min-w-[100px]">
            {sortOptions.map(option => (
              <div
                key={option.field}
                className={cn(
                  `px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 ${
                    activeField === option.field ? 'bg-white dark:bg-neutral-700 font-semibold' : ''
                  }`,
                )}
                onClick={() => setActiveField(option.field)}
              >
                {option.label}
              </div>
            ))}
          </div>

          {/* 二级菜单 */}
          <div className="flex flex-col">
            {activeField &&
              sortOptions
                .find(option => option.field === activeField)
                ?.directions.map(dir => (
                  <div
                    key={dir.order}
                    className={cn(
                      `px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-neutral-700 ${
                        SortOrder === dir.order ? 'bg-white dark:bg-neutral-700 font-semibold' : ''
                      }`,
                      menuSizeClasses[size],
                    )}
                    onClick={() => {
                      onChange(activeField, dir.order)
                      setSortOrder(dir.order)
                    }}
                  >
                    {dir.label}
                  </div>
                ))}
          </div>
        </div>
      )}
    </div>
  )
}

const MaterialFilterBar: React.FC<FilterProps> = ({ filters, orientation, setFilters, setOrientation }) => {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  return (
    <>
      <style>{datepickerCustomStyles}</style>
      <div className="flex justify-between">
        <div className="flex flex-1 items-center space-x-2 mr-4 text-sm flex-wrap">
          <div className="mb-2">
            <DatePicker
              selectsRange
              startDate={startDate}
              endDate={endDate}
              onChange={(dates: [Date | null, Date | null]) => {
                const [start, end] = dates
                setStartDate(start)
                setEndDate(end)
                console.log('???');
                
                setFilters((prev: any) => ({
                  ...prev,
                  // createAtRange0: start ? start.toLocaleDateString('zh-CN').replace(/\//g, '-') : null,
                  // createAtRange1: end ? end.toLocaleDateString('zh-CN').replace(/\//g, '-') : null,
                  createAtRange0: start ? start.getTime() : null,
                  createAtRange1: end ? end.getTime() : null,
                  version: Date.now(),
                }))
              }}
              isClearable
              placeholderText="开始日期-结束日期"
              className="bg-white/5 border px-2 min-w-70 py-2 rounded-md focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              dateFormat="yyyy-MM-dd"
            />
          </div>
          <Select
            value={
              filters.durationRange0 !== null && filters.durationRange1 !== null
                ? JSON.stringify([filters.durationRange0, filters.durationRange1])
                : ''
            }
            onValueChange={value => {
              if (value === '__clear__') {
                // 清空选择
                setFilters(prev => ({ ...prev, durationRange0: null, durationRange1: null }))
                return
              }
              const range = JSON.parse(value)
              setFilters(prev => ({ ...prev, durationRange0: range[0], durationRange1: range[1] }))
            }}
          >
            <SelectTrigger className="min-w-32 max-w-50 mb-2 bg-white/5 py-4.5">
              <SelectValue placeholder="素材时长" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__clear__">全部</SelectItem>
              {durationOptions.map(opt => (
                <SelectItem key={opt.label} value={JSON.stringify(opt.value)}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <SearchInput
            placeholder="合成次数"
            value={filters.useCountRange}
            onChange={e => setFilters(prev => ({ ...prev, useCountRange: e.target.value }))}
            containerClassName="min-w-32 max-w-50"
            inputClassName="py-4.5 bg-white/5"
          />
          <SearchInput
            placeholder="引用次数"
            value={filters.quoteCountRange}
            onChange={e => setFilters(prev => ({ ...prev, quoteCountRange: e.target.value }))}
            containerClassName="min-w-32 max-w-50"
            inputClassName="py-4.5 bg-white/5"
          />
        </div>
        <div>
          <div className="flex items-center space-x-2 flex-nowrap text-sm mb-2">
            <SortMenu
              sortField={filters.sortField}
              sortOrder={filters.sortOrder}
              onChange={(field, order) =>
                setFilters(prev => ({
                  ...prev,
                  sortField: field,
                  sortOrder: order,
                }))
              }
            />
            <div className="bg-white/5 py-0.5 border rounded-sm flex items-center space-x-2 shadow-sm">
              <Button
                variant="link"
                className={`${orientation === 'horizontal' ? '' : 'text-muted-foreground'}`}
                onClick={() => setOrientation(MaterialResource.MediaStyle.HORIZONTAL)}
              >
                <GalleryVertical className="inline-block mr-1 w-4 h-4" />
                横图
              </Button>
              <Button
                variant="link"
                className={`${orientation === 'vertical' ? '' : 'text-muted-foreground'}`}
                onClick={() => setOrientation(MaterialResource.MediaStyle.VERTICAL)}
              >
                <GalleryHorizontal className="inline-block mr-1 w-4 h-4" />
                竖图
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default MaterialFilterBar
const datepickerCustomStyles = `
  .react-datepicker__header {
    background-color: #1e1e1e !important;
    color: #fff !important;
  }
  .react-datepicker {
    background-color: #1e1e1e !important;
    color: #fff !important;
  }
  .react-datepicker__current-month, .react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name{
    color: #fff !important;
  }
  .react-datepicker__day:hover{
    color: #000 !important;
  }
`
