import { Injectable } from '@nestjs/common'
import { app, BrowserWindow } from 'electron'
import { AutoUpdater } from '../../app-modules/AutoUpdater.js'
import { UpdateCheckResult, UpdateStartResult } from '@app/shared/types/ipc/auto-updater.js'

@Injectable()
export class AutoUpdaterService {
  
  /**
   * 检查更新
   */
  async checkForUpdates(): Promise<UpdateCheckResult> {
    const updater = new AutoUpdater()
    const result = await updater.getAutoUpdater().checkForUpdates()
    
    if (result?.updateInfo?.version && result.updateInfo.version !== app.getVersion()) {
      return { hasUpdate: true, version: result.updateInfo.version }
    }
    
    return { hasUpdate: false }
  }

  /**
   * 开始更新
   */
  async startUpdate(): Promise<UpdateStartResult> {
    const updater = new AutoUpdater()
    const autoUpdater = updater.getAutoUpdater()

    // 添加下载进度监听
    autoUpdater.on('download-progress', progressObj => {
      const progress = Math.round(progressObj.percent)
      console.log(`[AutoUpdaterService] 下载进度: ${progress}%`)

      // 发送进度事件到所有渲染进程
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(window => {
        window.webContents.send('update-download-progress', {
          progress,
          bytesPerSecond: progressObj.bytesPerSecond,
          percent: progressObj.percent,
          total: progressObj.total,
          transferred: progressObj.transferred
        })
      })
    })

    // 添加下载完成监听
    autoUpdater.on('update-downloaded', info => {
      console.log('[AutoUpdaterService] 更新下载完成:', info.version)

      // 发送下载完成事件到所有渲染进程
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(window => {
        window.webContents.send('update-downloaded', {
          version: info.version
        })
      })
    })

    // 添加错误监听
    autoUpdater.on('error', error => {
      console.error('[AutoUpdaterService] 更新出错:', error)

      // 发送错误事件到所有渲染进程
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(window => {
        window.webContents.send('update-error', {
          error: error.message
        })
      })
    })

    try {
      // 开始下载更新
      await autoUpdater.downloadUpdate()
      return { started: true }
    } catch (error) {
      console.error('[AutoUpdaterService] 启动更新失败:', error)
      throw error
    }
  }

  /**
   * 安装更新并重启应用
   */
  installUpdate(): void {
    const updater = new AutoUpdater()
    updater.getAutoUpdater().quitAndInstall()
  }
}
