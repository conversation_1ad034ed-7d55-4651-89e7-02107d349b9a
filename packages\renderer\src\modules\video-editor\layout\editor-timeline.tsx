import React from 'react'
import { Timeline } from '@/modules/video-editor/components/timeline/timeline'
import { useTimelineShortcuts } from '@/modules/video-editor/hooks/useTimelineShortcuts'

export const EditorTimeline: React.FC = () => {
  const refCallbacks = useTimelineShortcuts()

  return (
    <div
      className="flex-1 overflow-y-hidden"
      ref={ref => {
        refCallbacks.forEach(refCallback => refCallback(ref))
      }}
    >
      <div className="h-full overflow-y-auto bg-gray-900" id="TimelineScrollContainer">
        <Timeline />
      </div>
    </div>
  )
}
