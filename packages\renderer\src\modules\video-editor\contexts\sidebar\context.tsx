import { createContext, useContext } from 'react'
import { useSidebar as useUISidebar } from '@/components/ui/sidebar'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export type SidebarContextType = {
  activeResourcePanel: ResourcePlugins
  setActiveResourcePanel: (panel: ResourcePlugins) => void
  setIsOpen: (open: boolean) => void
}

// 创建 context，初始值为 undefined
export const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

// 自定义 hook 来使用 sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext)
  const uiSidebar = useUISidebar()

  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }

  return {
    ...context,
    setIsOpen: uiSidebar.setOpen,
  }
}
