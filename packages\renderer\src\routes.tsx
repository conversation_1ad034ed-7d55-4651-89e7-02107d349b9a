import React, { lazy, Suspense } from 'react'
import { Navigate, redirect, type RouteObject } from 'react-router-dom'
import { PageLoading } from '@/components/LoadingIndicator'
import { HomeLayout } from '@/components/layouts/HomeLayout'

import { Matrix } from '@/pages/Matrix'
import { MatrixSummary } from '@/pages/Matrix/summary'
import { ResourceModule } from '@/libs/request/api/resource'
import { AccountAuthorization } from '@/pages/Matrix/account/auth'
import { AccountList } from '@/pages/Matrix/account/list'
import { VideoPublishMain } from '@/pages/Matrix/publish/VideoPublishMain'
import { VideoPublishDraft } from '@/pages/Matrix/draft'
import { ProtectedRoute, PublicRoute } from '@/components/auth/AuthGuard'
import { PlanDetailList } from '@/pages/Matrix/planDetail/list'
import { AppLayout } from '@/components/layouts/AppLayout'
import { UploadTaskExample } from './components/UploadTasks/UploadTaskExample'

// 懒加载页面组件
const Dashboard = lazy(() => import('@/pages/Dashboard'))
const Projects = lazy(() => import('@/pages/Projects'))
const ProjectContent = lazy(() => import('@/pages/Projects/content'))
const ProjectEmpty = lazy(() => import('@/pages/Projects/empty'))
const ProjectCreative = lazy(() => import('@/pages/Projects/creative'))
const ProjectWorks = lazy(() => import('@/pages/Projects/works'))
const ProjectMaterial = lazy(() => import('@/pages/Projects/material'))
const ProjectRecycleBin = lazy(() => import('@/pages/Projects/recycleBin'))
const LoginLayout = lazy(() => import('@/pages/Login'))
const LoginPage = lazy(() => import('@/pages/Login/login'))
const LoginTeamInit = lazy(() => import('@/pages/Login/team'))
const TeamPage = lazy(() => import('@/pages/team'))

// 路由配置
export const routes: RouteObject[] = [
  {
    path: '/',
    element: (
      <Suspense fallback={<PageLoading />}>
        <AppLayout />
      </Suspense>
    ),
    children: [
      {
        index: true,
        loader: () => redirect('home'),
      },
      {
        path: 'login',
        element: (
          <PublicRoute>
            <Suspense fallback={<PageLoading />}>
              <LoginLayout />
            </Suspense>
          </PublicRoute>
        ),
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <LoginPage />
              </Suspense>
            ),
          },
          {
            path: 'team',
            element: (
              <Suspense fallback={<PageLoading />}>
                <LoginTeamInit />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: '/home',
        element: (
          <ProtectedRoute>
            <HomeLayout />
          </ProtectedRoute>
        ),
        children: [
          {
            path: 'dashboard',
            element: (
              <Suspense fallback={<PageLoading />}>
                <Dashboard />
              </Suspense>
            ),
          },
          {
            path: 'projects',
            element: (
              <Suspense fallback={<PageLoading />}>
                <Projects />
              </Suspense>
            ),
            children: [
              {
                index: true,
                loader: async () => {
                  try {
                    const { list } = await ResourceModule.project.page({})
                    if (list.length > 0) {
                      return redirect(list[0].id.toString())
                    }
                  } catch {
                    //
                  }
                },
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <ProjectEmpty />
                  </Suspense>
                ),
              },
              {
                path: ':projectId',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <ProjectContent />
                  </Suspense>
                ),
                children: [
                  {
                    index: true,
                    loader: args => {
                      console.log(args.context)
                      return redirect('creative')
                    },
                  },
                  {
                    // 我的创意
                    path: 'creative',
                    element: (
                      <Suspense fallback={<PageLoading />}>
                        <ProjectCreative />
                      </Suspense>
                    ),
                  },
                  {
                    // 我的素材
                    path: 'material',
                    element: (
                      <Suspense fallback={<PageLoading />}>
                        <ProjectMaterial />
                      </Suspense>
                    ),
                  },
                  {
                    // 我的作品
                    path: 'works',
                    element: (
                      <Suspense fallback={<PageLoading />}>
                        <ProjectWorks />
                      </Suspense>
                    ),
                  },
                  {
                    // 回收站
                    path: 'recycle-bin',
                    element: (
                      <Suspense fallback={<PageLoading />}>
                        <ProjectRecycleBin />
                      </Suspense>
                    ),
                  },
                ],
              },
            ],
          },
          {
            path: 'matrix',
            element: (
              <Suspense fallback={<PageLoading />}>
                <Matrix />
              </Suspense>
            ),
            children: [
              {
                index: true,
                element: <Navigate to="summary" replace />,
              },
              {
                // 矩阵宝数据统计
                path: 'summary',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <MatrixSummary />
                  </Suspense>
                ),
              },
              {
                path: 'planDetail/:planId',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <PlanDetailList />
                  </Suspense>
                ),
              },
              {
                // 矩阵宝分发管理
                path: 'distribution',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <VideoPublishMain />
                  </Suspense>
                ),
              },
              {
                // 账号授权
                path: 'auth',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <AccountAuthorization />
                  </Suspense>
                ),
              },
              {
                // 账号管理
                path: 'list',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <AccountList />
                  </Suspense>
                ),
              },
              {
                // 草稿箱管理
                path: 'draft',
                element: (
                  <Suspense fallback={<PageLoading />}>
                    <VideoPublishDraft />
                  </Suspense>
                ),
              },
            ],
          },
          {
            path: 'team',
            element: (
              <Suspense fallback={<PageLoading />}>
                <TeamPage />
              </Suspense>
            ),
          },
          {
            path: 'error-demo',
            element: (
              <Suspense fallback={<PageLoading />}>
                <UploadTaskExample />
              </Suspense>
            ),
          },
          {
            path: '',
            element: <Navigate to="/home/<USER>" replace />,
          },
        ],
      },
    ],
  },
]
