import React, { useCallback } from 'react'
import { FontResource } from '@/types/resources'
import { ResourceCollectionIndicator } from '@/modules/video-editor/components/common/resource-collection-indicator'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { useMutation } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { cn } from '@/components/lib/utils'

interface FontPreviewItemProps {
  font: FontResource.Font
  isSelected: boolean
  onFontChange: (font: FontResource.Font) => Promise<void>
  onCollectionChange?: (collected: boolean) => void
  className?: string
}

export const FontPreviewItem: React.FC<FontPreviewItemProps> = ({
  font,
  isSelected,
  onFontChange,
  onCollectionChange,
  className = ''
}) => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: onFontChange
  })

  const handleClick = useCallback(() => {
    return mutateAsync(font)
  }, [font.id, mutateAsync])

  return (
    <div
      className={cn(
        `flex items-center justify-between px-2 py-1.5 cursor-pointer
        hover:bg-accent/50 rounded-sm transition-colors`,
        isSelected ? 'bg-accent' : '',
        className
      )}
    >
      <div className="flex items-center gap-3 flex-1 min-w-0">
        {/* 收藏按钮 */}
        <div className="flex-shrink-0" onClick={e => e.stopPropagation()}>
          <ResourceCollectionIndicator
            resourceType={ResourceType.FONT}
            resourceId={font.id}
            isCollected={font.interactInfo?.collected || false}
            size={14}
            onCollectionChange={onCollectionChange}
          />
        </div>

        {/* 字体预览图 */}
        <div className="flex-shrink-0 flex-1 h-8 rounded overflow-hidden" onClick={handleClick}>
          {font.cover?.url ? (
            <img
              src={font.cover.url}
              alt={`${font.title} 预览`}
              className="w-full h-full object-contain"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <span className="text-xs font-medium text-foreground/70">Aa</span>
            </div>
          )}
        </div>

        {/* 选中状态指示器 */}
        <div className="flex-shrink-0 size-3.5 flex justify-end" onClick={handleClick}>
          {
            isPending
              ? <Loader2 className="w-3.5 h-3.5 animate-spin" />
              : isSelected
                ? <div className="w-2 h-2 bg-primary rounded-full" />
                : <div className="w-2 h-2 bg-muted rounded-full" />
          }
        </div>
      </div>
    </div>
  )
}
