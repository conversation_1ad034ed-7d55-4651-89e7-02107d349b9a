import React, { useState } from 'react'
import { useSidebar as useUISidebar } from '@/components/ui/sidebar'
import { SidebarContext } from './context'
import { ResourcePlugins } from '@/modules/video-editor/resource-plugin-system'

export const SidebarProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activeResourcePanel, setActiveResourcePanel] = useState<ResourcePlugins>(null as any)
  const { setOpen } = useUISidebar()

  return (
    <SidebarContext.Provider
      value={{
        activeResourcePanel,
        setActiveResourcePanel,
        setIsOpen: setOpen,
      }}
    >
      {children}
    </SidebarContext.Provider>
  )
}
