import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import { SearchIcon, X } from 'lucide-react'
import { usePagination } from '@/hooks/usePagination'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useQueryAccountGroup } from '@/hooks/queries/useQueryAccountGroup'
import { GroupSidebar } from '@/components/ui/group-sidebar'
import { Account, GroupAccount, GroupAccountSearchParams } from '@/types/matrix/douyin'

// 组件 ref 接口
export interface SelectAccountDialogRef {
  open: (options?: {
    selectedAccountIds?: number[]
    title?: string
    description?: string
    maxSelection?: number
    groupId?: number
  }) => void
  close: () => void
}

interface SelectAccountDialogProps {
  onConfirm: (selectedAccounts: Account[]) => void
}

export const SelectAccountDialog = forwardRef<SelectAccountDialogRef, SelectAccountDialogProps>(
  function SelectAccountDialog({ onConfirm }, ref) {
    const [open, setOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')

    const [selectedGroupId, setSelectedGroupId] = useState<number>(0)
    const [internalSelectedIds, setInternalSelectedIds] = useState<number[]>([])
    const [selectedAccountsMap, setSelectedAccountsMap] = useState<Map<number, Account>>(new Map())
    const [title, setTitle] = useState('选择账号')
    const [description, setDescription] = useState('请选择要使用的账号')
    const [maxSelection, setMaxSelection] = useState<number | undefined>(undefined)

    // 获取分组列表
    const { data: groups = [] } = useQueryAccountGroup()

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (options = {}) => {
        const selectedAccountIds = options.selectedAccountIds || []
        setInternalSelectedIds(selectedAccountIds)
        setTitle(options.title || '选择账号')
        setDescription(options.description || '请选择要使用的账号')
        setMaxSelection(options.maxSelection)

        // 设置默认分组（如果指定了分组ID）
        if (options.groupId) {
          setSelectedGroupId(options.groupId)
        }

        setOpen(true)
      },
      close: () => {
        setOpen(false)
        // 重置状态
        setSearchTerm('')
        setSelectedGroupId(0)
        setInternalSelectedIds([])
        setSelectedAccountsMap(new Map())
      }
    }), [])

    // 搜索防抖
    const debouncedSearchTerm = useDebounceValue(searchTerm, 500)

    // 构建搜索参数
    const searchParams = useMemo(() => ({
      groupId: selectedGroupId,
      search: debouncedSearchTerm || undefined,
    }), [selectedGroupId, debouncedSearchTerm])

    // 使用分页hook获取账户数据
    const {
      data: accounts,
      pagination,
      isLoading,
      isError,
      setPagination,
    } = usePagination<GroupAccount, GroupAccountSearchParams>({
      queryKey: [QUERY_KEYS.GROUP_ACCOUNT_LIST],
      queryFn: MatrixModule.groupAccount,
      searchParams,
      initialPageSize: 20,
      enabled: open && selectedGroupId > 0,
    })

    // 由于使用了分组账号API，账号已经按分组筛选，这里只需要显示
    const filteredAccounts = accounts

    // 动态补充已选账号的详细信息
    React.useEffect(() => {
      if (accounts && accounts.length > 0) {
        // 检查当前页面的账号中是否有已选中但未在 selectedAccountsMap 中的账号
        const accountsToAdd = accounts.filter(account =>
          internalSelectedIds.includes(account.id) && !selectedAccountsMap.has(account.id)
        )

        if (accountsToAdd.length > 0) {
          setSelectedAccountsMap(prev => {
            const newMap = new Map(prev)
            accountsToAdd.forEach(account => {
              newMap.set(account.id, account)
            })
            return newMap
          })
        }
      }
    }, [accounts, internalSelectedIds, selectedAccountsMap])

    // 处理选择
    const handleSelectAccount = (accountId: number, selected: boolean) => {
      if (selected) {
        if (maxSelection && internalSelectedIds.length >= maxSelection) {
          return
        }
        // 添加到选择列表
        setInternalSelectedIds(prev => [...prev, accountId])

        // 添加到账户映射表（动态获取当前页面的账号信息）
        const account = accounts?.find(acc => acc.id === accountId)
        if (account) {
          setSelectedAccountsMap(prev => new Map(prev).set(accountId, account))
        }
      } else {
        // 从选择列表移除
        setInternalSelectedIds(prev => prev.filter(id => id !== accountId))

        // 从账户映射表移除
        setSelectedAccountsMap(prev => {
          const newMap = new Map(prev)
          newMap.delete(accountId)
          return newMap
        })
      }
    }

    // 处理全选
    const handleSelectAll = (selected: boolean) => {
      if (selected) {
        const newAccounts = filteredAccounts
          ?.filter(account => !internalSelectedIds.includes(account.id))
          .slice(0, maxSelection ? maxSelection - internalSelectedIds.length : filteredAccounts.length) || []

        const newIds = newAccounts.map(account => account.id)
        setInternalSelectedIds(prev => [...prev, ...newIds])

        // 添加到账户映射表
        setSelectedAccountsMap(prev => {
          const newMap = new Map(prev)
          newAccounts.forEach(account => {
            newMap.set(account.id, account)
          })
          return newMap
        })
      } else {
        const currentPageIds = filteredAccounts?.map(account => account.id) || []
        setInternalSelectedIds(prev => prev.filter(id => !currentPageIds.includes(id)))

        // 从账户映射表移除
        setSelectedAccountsMap(prev => {
          const newMap = new Map(prev)
          currentPageIds.forEach(id => newMap.delete(id))
          return newMap
        })
      }
    }

    // 确认选择
    const handleConfirm = () => {
      const selectedAccounts = Array.from(selectedAccountsMap.values())
      onConfirm(selectedAccounts)
      setOpen(false)
    }

    // 取消选择
    const handleCancel = () => {
      setOpen(false)
      // 重置状态
      setSearchTerm('')
      setSelectedGroupId(0)
      setInternalSelectedIds([])
      setSelectedAccountsMap(new Map())
    }

    // 处理分组选择
    const handleGroupSelect = (groupId: number) => {
      setSelectedGroupId(groupId)
    }

    // 表格列定义
    const columns: ColumnDef<GroupAccount>[] = [
      {
        id: 'select',
        header: () => {
          const isAllSelected = filteredAccounts.length > 0 &&
            filteredAccounts.every(account => internalSelectedIds.includes(account.id))
          const isIndeterminate = filteredAccounts.some(account => internalSelectedIds.includes(account.id)) && !isAllSelected

          return (
            <Checkbox
              checked={isAllSelected}
              ref={(el: HTMLButtonElement | null) => {
                if (el) (el as any).indeterminate = isIndeterminate
              }}
              onCheckedChange={handleSelectAll}
              aria-label="选择全部"
            />
          )
        },
        cell: ({ row }) => (
          <Checkbox
            checked={internalSelectedIds.includes(row.original.id)}
            onCheckedChange={checked => handleSelectAccount(row.original.id, !!checked)}
            aria-label="选择行"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: 'avatar',
        header: '头像',
        cell: ({ row }) => (
          <Avatar className="h-8 w-8">
            <AvatarImage src={row.original.avatar} alt={row.original.nickname} />
            <AvatarFallback>
              {row.original.nickname?.charAt(0) || '?'}
            </AvatarFallback>
          </Avatar>
        ),
      },
      {
        accessorKey: 'nickname',
        header: '账号名称',
        cell: ({ row }) => (
          <span className="font-medium">{row.original.nickname || '-'}</span>
        ),
      },
      {
        accessorKey: 'createTime',
        header: '创建时间',
        cell: ({ row }) => (
          <span>{row.original.createTime ? new Date(row.original.createTime).toLocaleDateString() : '-'}</span>
        ),
      },
    ]

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-[1200px] max-h-[90vh] min-w-[1200px] flex flex-col">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>{description}</DialogDescription>
          </DialogHeader>

          <div className="flex flex-1 gap-4 min-h-0">
            {/* 左侧分组选择器 */}
            <div className="w-64 border-r pr-4">
              <GroupSidebar
                groups={groups}
                selectedGroupId={selectedGroupId}
                onGroupSelect={handleGroupSelect}
                showOptions={false}
              />
            </div>

            {/* 中间账号列表 */}
            <div className="flex-1 flex flex-col min-h-0">
              {/* 搜索和筛选 */}
              <div className="flex items-center gap-4 mb-4">
                <div className="relative flex-1">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索账号名称"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>

              </div>

              {/* 账号表格 */}
              <div className="flex-1 min-h-0">
                {selectedGroupId === 0 ? (
                  <div className="flex items-center justify-center h-32 text-muted-foreground">
                    请选择一个分组查看账号列表
                  </div>
                ) : (
                  <DataTable
                    columns={columns}
                    data={filteredAccounts}
                    pagination={pagination}
                    onPaginationChange={setPagination}
                    loading={isLoading}
                    emptyMessage={isError ? '加载失败，请重试' : '暂无账号数据'}
                  />
                )}
              </div>
            </div>

            {/* 右侧已选择账户列表 */}
            <div className="w-80 border-l pl-4 flex flex-col min-h-0">
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">
                  已选择账户 ({internalSelectedIds.length})
                  {maxSelection && ` / ${maxSelection}`}
                </h3>
                {internalSelectedIds.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setInternalSelectedIds([])
                      setSelectedAccountsMap(new Map())
                    }}
                    className="w-full"
                  >
                    清空全部
                  </Button>
                )}
              </div>

              {/* 已选择账户列表 */}
              <div className="flex-1 overflow-y-auto">
                {internalSelectedIds.length === 0 ? (
                  <div className="flex items-center justify-center h-32 text-muted-foreground text-sm">
                    暂未选择账户
                  </div>
                ) : (
                  <div className="space-y-2">
                    {Array.from(selectedAccountsMap.values()).map(account => (
                      <div
                        key={account.id}
                        className="flex items-center gap-3 p-3 border rounded-lg bg-muted/30"
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={account.avatar} alt={account.nickname} />
                          <AvatarFallback>
                            {account.nickname?.charAt(0) || '?'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {account.nickname || account.orgNickname || '-'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ID: {account.id}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelectAccount(account.id, false)}
                          className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <Button onClick={handleConfirm} disabled={internalSelectedIds.length === 0}>
              确定 ({internalSelectedIds.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
)
