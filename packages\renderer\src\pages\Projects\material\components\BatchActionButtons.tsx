import React from 'react'
import { Button } from '@/components/ui/button'
import { usePending } from '@/hooks/usePending'
import { useDeleteModal } from '@/components/modal/delete'
import { Loader2 } from 'lucide-react'

type BatchActionButtonsProps = {
  variant?: 'default' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon' | null | undefined
  highlightClass?: string
  deleteKind?: string
  deleteName?: string
  onMove?: () => void
  onDelete?: (isPermanent: boolean) => Promise<void>
}

const BatchActionButtons: React.FC<BatchActionButtonsProps> = ({
  variant = 'default',
  size = 'sm',
  highlightClass = 'bg-primary-highlight1 hover:bg-blue-400 text-white',
  deleteKind = '所选',
  deleteName = '文件',
  onMove,
  onDelete
}) => {
  const { pending, withPending } = usePending()
  const deleteModal = useDeleteModal()

  const handleDelete = () => {
    deleteModal({
      kind: deleteKind,
      name: deleteName,
      danger: true,
      buttons: ({ close }) => (
        <>
          <Button
            variant="default"
            className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
            onClick={withPending(async () => {
              await onDelete?.(false)
              close()
            })}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : '放入回收站'}
          </Button>
          <Button
            variant="destructive"
            className="min-w-[80px] h-8 ml-2 bg-destructive text-white border hover:bg-destructive/90"
            onClick={withPending(async () => {
              await onDelete?.(true)
              close()
            })}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : '彻底删除'}
          </Button>
        </>
      ),
    })
  }

  return (
    <div className="flex items-center">
      <Button variant={variant} size={size} className={highlightClass} onClick={onMove}>
        移动到
      </Button>
      {variant === 'link' && <span className="text-primary-highlight1 mx-1"> | </span>}
      <Button
        variant={variant}
        size={size}
        className={highlightClass + (variant === 'default' ? ' ml-2' : '')}
        onClick={handleDelete}
      >
        删除
      </Button>
    </div>
  )
}

export default BatchActionButtons
