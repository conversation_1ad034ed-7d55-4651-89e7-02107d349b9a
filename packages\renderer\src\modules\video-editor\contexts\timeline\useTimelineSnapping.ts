import { useEffect, useMemo, useState } from 'react'
// Adjust path as needed
import { SNAPPING_CONFIG } from '../../constants'
import { TimelineOverlayDnDHook } from './useTimelineDragAndDrop'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { GhostElement } from '@/modules/video-editor/types'

import { getOverlayTimeRange } from '@/modules/video-editor/utils/overlay-helper'

// Define the snap target type
interface SnapTarget {
  targetFrame: number // The frame of the edge we are aligning TO
  ghostEdge: 'start' | 'end' // Which edge of the GHOST is close?
}

type UseTimelineSnappingProps =
  & Pick<TimelineOverlayDnDHook, 'isDragging' | 'landingPoint' | 'dragInfo' | 'draggingOverlay'>
  & {
    snapThreshold?: number // Threshold now defaults using the constant
  }

export interface TimelineSnappingHook {
  alignmentLines: number[]
  snappedLandingPoint: GhostElement | null
}

export const useTimelineSnapping = ({
  isDragging,
  landingPoint: ghostElement,
  draggingOverlay,
  dragInfo,
  snapThreshold = SNAPPING_CONFIG.thresholdFrames, // Use constant for default
}: UseTimelineSnappingProps): TimelineSnappingHook => {
  const { tracks, durationInFrames } = useEditorContext()
  const visibleRows = useMemo(() => tracks.length, [tracks])

  const [alignmentLines, setAlignmentLines] = useState<number[]>([])
  const [snapTargets, setSnapTargets] = useState<SnapTarget[]>([])
  const [snappedLandingPoint, setSnappedLandingPoint] = useState<GhostElement | null>(null)

  // Effect 1: Calculate Potential Snap Targets and Alignment Lines
  useEffect(() => {
    if (!(
      SNAPPING_CONFIG.enableVerticalSnapping
      && isDragging
      && ghostElement
      && draggingOverlay
      && visibleRows > 0
      && dragInfo.current
    )) {
      if (alignmentLines.length > 0) setAlignmentLines([])
      if (snapTargets.length > 0) setSnapTargets([])
      return
    }

    const targetRowIndex = ghostElement.row

    const [ghostStartFrame, ghostEndFrame] = getOverlayTimeRange(ghostElement)

    const newAlignmentLines = new Set<number>()
    const newSnapTargets: SnapTarget[] = []

    // 仅在拖动目标轨道（即鼠标悬浮的轨道）的上下两个轨道之内寻找
    const targetTracks = tracks
      .filter((_, trackIndex) => {
        return trackIndex === targetRowIndex - 1
          || trackIndex === targetRowIndex + 1
      })

    const targetOverlays = targetTracks
      .map(o => o.overlays)
      .flat()
      .filter(overlay => overlay.id !== draggingOverlay.id)

    targetOverlays
      .forEach(overlay => {
        const otherStartFrame = overlay.from
        const otherEndFrame = overlay.from + overlay.durationInFrames

        if (Math.abs(ghostStartFrame - otherStartFrame) <= snapThreshold) {
          newAlignmentLines.add(otherStartFrame)
          newSnapTargets.push({
            targetFrame: otherStartFrame,
            ghostEdge: 'start',
          })
        }
        if (Math.abs(ghostStartFrame - otherEndFrame) <= snapThreshold) {
          newAlignmentLines.add(otherEndFrame)
          newSnapTargets.push({
            targetFrame: otherEndFrame,
            ghostEdge: 'start',
          })
        }
        if (Math.abs(ghostEndFrame - otherStartFrame) <= snapThreshold) {
          newAlignmentLines.add(otherStartFrame)
          newSnapTargets.push({
            targetFrame: otherStartFrame,
            ghostEdge: 'end',
          })
        }
        if (Math.abs(ghostEndFrame - otherEndFrame) <= snapThreshold) {
          newAlignmentLines.add(otherEndFrame)
          newSnapTargets.push({
            targetFrame: otherEndFrame,
            ghostEdge: 'end',
          })
        }
      })

    const currentLines = Array.from(newAlignmentLines).sort((a, b) => a - b)

    if (JSON.stringify(currentLines) !== JSON.stringify(alignmentLines)) {
      setAlignmentLines(currentLines)
    }

    if (JSON.stringify(newSnapTargets) !== JSON.stringify(snapTargets)) {
      setSnapTargets(newSnapTargets)
    }
  }, [
    isDragging,
    ghostElement,
    draggingOverlay,
    tracks,
    durationInFrames,
    alignmentLines,
    snapTargets,
    snapThreshold,
    visibleRows,
    dragInfo,
  ])

  // Effect 2: Calculate the Snapped Ghost Element based on Action
  useEffect(() => {
    // Added check for dragInfo.current here as well
    if (
      !SNAPPING_CONFIG.enableVerticalSnapping // Check if vertical snapping is enabled
      || !isDragging
      || !ghostElement
      || !dragInfo.current
      || snapTargets.length === 0
    ) {
      if (snappedLandingPoint !== ghostElement) {
        setSnappedLandingPoint(ghostElement)
      }
      return
    }

    const action = dragInfo.current.action
    const [rawGhostStartFrame, rawGhostEndFrame] = getOverlayTimeRange(ghostElement)

    let bestSnapDiff = Infinity
    let bestSnapTarget: SnapTarget | null | any = null

    snapTargets.forEach((target: SnapTarget) => {
      let diff = Infinity
      if (action === 'move') {
        diff = Math.min(
          diff,
          Math.abs(rawGhostStartFrame - target.targetFrame),
        )
      }
      if (action === 'move' || action === 'resize-end') {
        diff = Math.min(diff, Math.abs(rawGhostEndFrame - target.targetFrame))
      }
      if (diff < bestSnapDiff && diff <= snapThreshold) {
        bestSnapDiff = diff
        bestSnapTarget = target
      }
    })

    if (bestSnapTarget === null) {
      if (snappedLandingPoint !== ghostElement) {
        setSnappedLandingPoint(ghostElement)
      }
      return
    }

    let snappedStartFrame: number | null = null
    let snappedEndFrame: number | null = null
    let snappedDurationFrames = Math.max(1, ghostElement.durationInFrames)
    const targetFrame = bestSnapTarget.targetFrame

    if (action === 'move') {
      if (Math.abs(rawGhostStartFrame - targetFrame) <= snapThreshold) {
        snappedStartFrame = Math.round(targetFrame)
      } else {
        snappedEndFrame = Math.round(targetFrame)
        snappedStartFrame = snappedEndFrame - snappedDurationFrames
      }
      if (snappedEndFrame === null && snappedStartFrame !== null) {
        snappedEndFrame = snappedStartFrame + snappedDurationFrames
      }
    } else if (action === 'resize-end') {
      snappedEndFrame = Math.round(targetFrame)
      snappedStartFrame = Math.round(rawGhostStartFrame)
      snappedDurationFrames = Math.max(
        1,
        snappedEndFrame - snappedStartFrame,
      )
      if (
        snappedDurationFrames === 1
        && snappedStartFrame >= snappedEndFrame
      ) {
        snappedStartFrame = snappedEndFrame - 1
      }
    }

    if (snappedStartFrame === null) {
      // Use raw ghost if calculation failed
      if (snappedLandingPoint !== ghostElement)
        setSnappedLandingPoint(ghostElement)
      return
    }

    const newGhost: GhostElement = {
      // Ensure newGhost always has top, even if original ghostElement was null briefly
      // Use a default or handle null case more robustly if ghostElement can be null initially
      ...ghostElement,
      durationInFrames: snappedDurationFrames
    }

    const isDifferent = !snappedLandingPoint
      || newGhost.from !== snappedLandingPoint.from
      || newGhost.durationInFrames !== snappedLandingPoint.durationInFrames
      || newGhost.row !== snappedLandingPoint.row

    if (isDifferent) {
      setSnappedLandingPoint(newGhost)
    }
  }, [
    ghostElement,
    snapTargets,
    isDragging,
    durationInFrames,
    snapThreshold,
    dragInfo,
    snappedLandingPoint,
  ])

  return { alignmentLines, snappedLandingPoint }
}
