import { rawInstance } from '@/libs/request/instance'

export async function parseUrlFromObjectHref(objectHref: string) {
  if (!/\/oss\/object-href\/\w+/.test(objectHref)) {
    return objectHref
  }

  const [fetchResult, fetchError] = await rawInstance
    .get(objectHref)
    .then(r => [r.data, null])
    .catch(error => [null, new Error(`Cannot fetch object href(${objectHref}): ${error.message}`)])

  if (fetchError) throw fetchError

  const [dom, parseError] = await new Promise<[Document, null] | [null, Error]>(
    (resolve, reject) => {
      try {
        return resolve([
          new DOMParser().parseFromString(fetchResult, 'text/html'),
          null
        ])
      } catch {
        return reject([
          null,
          new Error(`Failed to parse DOM from response data "${fetchResult}"`)
        ])
      }
    }
  )

  if (parseError) throw parseError

  const aTag = dom.querySelector('a')
  if (!aTag) throw new Error('No valid <a> tag in parsed dom')

  const href = aTag.href
  if (!href) throw new Error('No content in `href` of parsed <a> tag')

  return href
}
