
/**
 * 标准 API 响应结构
 */
export interface ApiResponse<T = any> {
  code: number
  data?: T
  msg?: string 
  error?: string
}

/**
 * 通用实体基础接口
 * 所有领域模型的基础类型
 */
export interface BaseEntity {
  id: any
  created_at: number
  updated_at: number
  deleted_at: number
}

/**
 * 通用时间戳字段接口
 */
export interface TimeStampFields {
  created_at?: number
  updated_at?: number
  deleted_at?: number
}

/**
 * 通用所有权字段接口
 */
export interface OwnershipFields {
  uid: string
  team_id?: number | null
}

/**
 * 通用分页查询结果
 */
export interface PaginatedResult<T> {
  list: T[]
  total: number
}

/**
 * 通用分页查询参数
 */
export interface PaginationParams {
  pageNo?: number
  pageSize?: number
  sortBy?: string
  order?: 'asc' | 'desc'
}

/**
 * 通用查询条件类型
 */
export type QueryConditions = Record<string, any>

/**
 * 通用排序选项
 */
export interface SortOptions {
  sortBy: string
  order: 'asc' | 'desc'
}

/**
 * 通用响应状态
 */
export enum ResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
}

/**
 * 文件夹相关类型
 */
export namespace Folder {
  /**
     * 文件夹基础接口
     */
  export interface IFolder extends BaseEntity, OwnershipFields {
    parent_id: number
    material_type: number
    path: string
    title: string
    auto_create: number
  }

  /**
 * 创建文件夹参数
 */
  export interface CreateParams extends OwnershipFields {
    parent_id?: number
    material_type?: number
    path?: string
    title: string
    auto_create?: number
  }

  /**
 * 更新文件夹参数
 */
  export interface UpdateParams extends TimeStampFields {
    parent_id?: number
    path?: string
    title?: string
    team_id?: number | null
    auto_create?: number
  }

  /**
 * 查询文件夹参数
 */
  export interface QueryParams extends Partial<OwnershipFields> {
    parent_id?: number
    deleted_at?: number
    material_type?: number
  }

  /**
     * 文件夹路径查询结果
     */
  export interface StatsResult {
    path: IFolder[]
    current: IFolder | null
  }
}

/**
 * 素材文件相关类型
 */
export namespace MaterialFile {
  /**
     * 素材文件类型枚举
     */
  export enum Type {
    VIDEO = 1,
    AUDIO = 2,
    IMAGE = 3,
  }

  /**
     * 素材文件状态枚举
     */
  export enum Status {
    UNPROCESSED = 0,
    PROCESSING = 1,
    COMPLETED = 2,
    FAILED = 3,
  }

  /**
     * 素材存储位置枚举
     */
  export enum StorageLocation {
    INITIAL = 0,
    CLOUD = 1,
    LOCAL = 2,
  }

  /**
     * 素材文件基础接口
     */
  export interface IMaterialFile extends BaseEntity, OwnershipFields {
    id: string
    parent_id: number
    material_type: number
    path: string
    source_path: string
    size: number
    duration: number
    width: number
    height: number
    codec_name: string
    codec_type: string
    bit_rate: number | null
    nb_frames: number | null
    cover: string
    hash: string
    status: number
    clip_cloud_or_local: number
    object_oid: string
    reason: string
    title: string
    upload_id: number
    task_no: string | null
    clipinfo: string
    tag_id: number
  }

  /**
     * 创建素材文件参数
     */
  export interface CreateParams extends OwnershipFields {
    id: string
    parent_id?: number
    material_type?: number
    path?: string
    source_path?: string
    size?: number
    duration?: number
    width?: number
    height?: number
    codec_name?: string
    codec_type?: string
    bit_rate?: number | null
    nb_frames?: number | null
    cover?: string
    hash?: string
    status?: number
    clip_cloud_or_local?: number
    object_oid?: string
    reason?: string
    title: string
    upload_id?: number
    task_no?: string | null
    clipinfo?: string
    tag_id?: number
  }

  /**
     * 更新素材文件参数
 */
  export interface UpdateParams extends TimeStampFields {
    parent_id?: number
    team_id?: number | null
    path?: string
    source_path?: string
    size?: number
    duration?: number
    width?: number
    height?: number
    codec_name?: string
    codec_type?: string
    bit_rate?: number | null
    nb_frames?: number | null
    cover?: string
    hash?: string
    status?: number
    clip_cloud_or_local?: number
    object_oid?: string
    reason?: string
    title?: string
    upload_id?: number
    task_no?: string | null
    clipinfo?: string
    tag_id?: number
  }

  /**
     * 查询素材文件参数
     */
  export interface QueryParams extends Partial<OwnershipFields> {
    id?: string
    parent_id?: number
    material_type?: number
    status?: number
    clip_cloud_or_local?: number
    deleted_at?: number
    tag_id?: number
  }

  /**
     * 素材文件搜索参数
     */
  export interface SearchParams extends Partial<OwnershipFields> {
    keyword: string
    types?: number[]
    startDate?: number
    endDate?: number
  }

  /**
     * 素材文件统计结果
     */
  export interface StatsResult {
    totalCount: number
    totalSize: number
    typeDistribution: Record<number, number>
  }
}

/**
 * 上传任务相关类型
 */
export namespace UploadTask {
  /**
   * 上传任务状态枚举
   */
  export enum Status {
    PENDING = 0,      // 等待上传
    UPLOADING = 1,    // 上传中
    PAUSED = 2,       // 已暂停
    COMPLETED = 3,    // 上传完成
    FAILED = 4,       // 上传失败
    CANCELLED = 5     // 已取消
  }

  /**
   * 上传任务类型枚举
   */
  export enum Type {
    VIDEO = 1,        // 视频文件
    AUDIO = 2,        // 音频文件
    IMAGE = 3,        // 图片文件
    OTHER = 4         // 其他文件
  }

  /**
   * 上传任务基础接口
   */
  export interface IUploadTask extends BaseEntity, OwnershipFields {
    id: number
    team_id: number
    name: string
    local_path: string
    url: string
    hash: string
    size: number
    progress: number
    status: Status
    type: Type
    reason: string
    folder_id: string
    object_key: string
    object_id: string
    upload_module: string
    checkpoint_data: string
  }

  /**
   * 创建上传任务参数
   */
  export interface CreateParams extends OwnershipFields {
    name: string
    local_path: string
    type?: Type
    folder_id?: string
    upload_module?: string
  }

  /**
   * 更新上传任务参数
   */
  export interface UpdateParams {
    name?: string
    url?: string
    hash?: string
    progress?: number
    status?: Status
    reason?: string
    cover?: string
    object_key?: string
    object_id?: string
    resume_data?: string
    checkpoint_data?: string
    size?: number
    local_path?: string
  }

  /**
   * 查询上传任务参数
   */
  export interface QueryParams extends Partial<OwnershipFields> {
    status?: Status | Status[]
    type?: Type | Type[]
    folder_id?: string
    keyword?: string
    start_date?: number
    end_date?: number
  }

  /**
   * 上传任务统计结果
   */
  export interface StatsResult {
    total_count: number
    pending_count: number
    uploading_count: number
    paused_count: number
    completed_count: number
    failed_count: number
    cancelled_count: number
    total_size: number
    uploaded_size: number
    type_distribution: Record<Type, number>
  }

  /**
   * 批量操作参数
   */
  export interface BatchParams {
    ids: number[]
    action: 'pause' | 'resume' | 'cancel' | 'retry' | 'delete'
  }

  /**
   * 上传进度事件数据
   */
  export interface ProgressEvent {
    task_id: number
    progress: number
    speed?: number
    estimated_time?: number
  }

  /**
   * 上传任务队列配置
   */
  export interface QueueConfig {
    max_concurrent_uploads: number
    retry_attempts: number
    retry_delay: number
    chunk_size: number
  }

  /**
   * 断点续传数据
   */
  export interface ResumeData {
    upload_id?: string
    part_number?: number
    etag?: string
    checkpoint?: any
  }
}

/**
 * 合成记录相关类型
 */
export namespace Compose {
  /**
   * 合成记录状态枚举
   */
  export enum Status {
    PENDING = 0,     // 等待中
    PROCESSING = 1,  // 处理中
    COMPLETED = 2,   // 已完成
    FAILED = 3       // 失败
  }

  /**
   * 下载状态枚举
   */
  export enum DownloadStatus {
    NOT_STARTED = 0, // 未开始
    DOWNLOADING = 1, // 下载中
    COMPLETED = 2,   // 已完成
    FAILED = 3       // 失败
  }

  /**
   * 合成记录基础接口
   */
  export interface ICompose extends BaseEntity {
    cid: number;            // 合成任务ID
    team_id: number;        // 团队ID
    script_id: string;      // 脚本ID
    url: string;            // 合成视频URL
    path: string;           // 本地下载路径
    object_oid: string;     // 合成文件对象ID
    name: string;           // 文件名
    cover: string;          // 云端封面图
    duration: number;       // 时长
    width: number;          // 宽
    height: number;         // 高
    size: number;           // 大小
    status: number;         // 状态
    reason: string;         // 失败原因
    download_count: number; // 下载次数
    download_progress: number; // 下载进度
    download_status: number;   // 下载状态
    download_reason: string;   // 下载失败原因
    read: number;           // 是否已读
    notify: number;         // 是否通知
    auto_download: number;  // 自动下载
    auto_download_path: string; // 自动下载路径
    group_At: number | null;    // 分组时间
    download_at: number | null; // 下载完成时间
  }

  /**
   * 创建合成记录参数
   */
  export interface CreateParams {
    cid?: number;           // 合成任务ID
    team_id?: number;       // 团队ID
    script_id?: string;     // 脚本ID
    url?: string;           // 合成视频URL
    path?: string;          // 本地下载路径
    object_oid?: string;    // 合成文件对象ID
    name: string;           // 文件名(必填)
    cover?: string;         // 云端封面图
    duration?: number;      // 时长
    width?: number;         // 宽
    height?: number;        // 高
    size?: number;          // 大小
    status?: number;        // 状态
    reason?: string;        // 失败原因
    download_count?: number; // 下载次数
    download_progress?: number; // 下载进度
    download_status?: number;   // 下载状态
    download_reason?: string;   // 下载失败原因
    read?: number;          // 是否已读
    notify?: number;        // 是否通知
    auto_download?: number; // 自动下载
    auto_download_path?: string; // 自动下载路径
    group_At?: number | null;    // 分组时间
    download_at?: number | null; // 下载完成时间
  }

  /**
   * 更新合成记录参数
   */
  export interface UpdateParams extends TimeStampFields {
    cid?: number;           // 合成任务ID
    team_id?: number;       // 团队ID
    script_id?: string;     // 脚本ID
    url?: string;           // 合成视频URL
    path?: string;          // 本地下载路径
    object_oid?: string;    // 合成文件对象ID
    name?: string;          // 文件名
    cover?: string;         // 云端封面图
    duration?: number;      // 时长
    width?: number;         // 宽
    height?: number;        // 高
    size?: number;          // 大小
    status?: number;        // 状态
    reason?: string;        // 失败原因
    download_count?: number; // 下载次数
    download_progress?: number; // 下载进度
    download_status?: number;   // 下载状态
    download_reason?: string;   // 下载失败原因
    read?: number;          // 是否已读
    notify?: number;        // 是否通知
    auto_download?: number; // 自动下载
    auto_download_path?: string; // 自动下载路径
    group_At?: number | null;    // 分组时间
    download_at?: number | null; // 下载完成时间
  }

  /**
   * 查询合成记录参数
   */
  export interface QueryParams {
    id?: number;            // 主键ID
    cid?: number;           // 合成任务ID
    team_id?: number;       // 团队ID
    script_id?: string;     // 脚本ID
    status?: number;        // 状态
    download_status?: number; // 下载状态
    read?: number;          // 是否已读
    notify?: number;        // 是否通知
    deleted_at?: number;    // 是否删除
  }

  /**
   * 搜索合成记录参数
   */
  export interface SearchParams {
    keyword: string;        // 搜索关键词
    team_id?: number;       // 团队ID
    statuses?: number[];    // 状态过滤
    startDate?: number;     // 开始日期时间戳
    endDate?: number;       // 结束日期时间戳
  }

  /**
   * 合成记录统计结果
   */
  export interface StatsResult {
    totalCount: number;         // 总数
    completedCount: number;     // 完成数
    failedCount: number;        // 失败数
    pendingCount: number;       // 等待数
    processingCount: number;    // 处理中数量
    downloadedCount: number;    // 已下载数量
  }
}
