import React from 'react'
import {
  <PERSON>lisionDetection,
  Modifier,
  MouseSensor,
  rectIntersection,
  TouchSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { ResourceSource } from '@/types/resources'
import { CSS } from '@dnd-kit/utilities'

export const pointerWithinFolder: CollisionDetection = args => {
  const pointerIntersections = rectIntersection(args)

  const { pointerCoordinates, droppableContainers } = args
  if (!pointerCoordinates) return []

  // 允许放置的文件夹类型列表
  const allowedFolderTypes = new Set([
    ResourceSource.LOCAL_STICK_FOLDER,
    ResourceSource.LOCAL_MUSIC_FOLDER,
    ResourceSource.LOCAL_SOUND_FOLDER,
    ResourceSource.FOLDER,
  ])

  return pointerIntersections.filter(({ id }) => {
    const droppable = droppableContainers.find(container => container.id === id)
    if (!droppable) return false

    const type = droppable.data.current?.type
    if (!type || !allowedFolderTypes.has(type)) return false

    const rect = droppable.rect.current
    if (!rect) return false
    return (
      pointerCoordinates.x >= rect.left &&
      pointerCoordinates.x <= rect.right &&
      pointerCoordinates.y >= rect.top &&
      pointerCoordinates.y <= rect.bottom
    )
  })
}

export const centerDragOverlay: Modifier = ({ transform }) => {
  return {
    ...transform,
    x: transform.x + 24, // 48 = w-24 的一半
    y: transform.y + 24, // 48 = h-24 的一半
  }
}

export function DraggableItem<T>({
  id,
  type,
  resource,
  children,
}: {
  id: string
  type: string
  resource: T
  children: React.ReactNode
}) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id,
    data: { type, ...resource },
  })

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={{
        transform: isDragging ? undefined : CSS.Translate.toString(transform), // 拖拽时不应用 transform
      }}
      className={isDragging ? 'opacity-50' : ''}
    >
      {children}
    </div>
  )
}

export const DroppableItem: React.FC<{
  id: string
  type: ResourceSource
  children: React.ReactNode
}> = ({ id, type, children }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: { type },
  })

  return (
    <div ref={setNodeRef} style={{ background: isOver ? 'rgba(0,0,0,0.05)' : undefined }}>
      {children}
    </div>
  )
}

export const useFolderDndSensors = () => {
  return useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 5,
      },
    }),
  )
}
