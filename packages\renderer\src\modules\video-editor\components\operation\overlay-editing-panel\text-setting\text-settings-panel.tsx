import React from 'react'
import { AnimationSettings } from '../../../shared/animation-preview'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { useTextSettingContext } from './context'

export const TextSettingsPanel: React.FC = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const handleEnterAnimationSelect = (animationKey: string) => {
    requestUpdateText({
      styles: {
        animation: {
          ...textOverlay.styles.animation,
          enter: animationKey === 'none' ? undefined : animationKey,
        },
      }
    }, true)
  }

  const handleExitAnimationSelect = (animationKey: string) => {
    requestUpdateText({
      styles: {
        animation: {
          ...textOverlay.styles.animation,
          exit: animationKey === 'none' ? undefined : animationKey,
        },
      }
    }, true)
  }

  return (
    <AnimationSettings
      animations={animationTemplates}
      selectedEnterAnimation={textOverlay.styles.animation?.enter}
      selectedExitAnimation={textOverlay.styles.animation?.exit}
      onEnterAnimationSelect={handleEnterAnimationSelect}
      onExitAnimationSelect={handleExitAnimationSelect}
    />
  )
}
