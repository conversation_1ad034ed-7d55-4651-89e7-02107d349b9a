import axios, { type AxiosInstance } from 'axios'
import { BASE_URL, DEFAULT_HEADERS, TIMEOUT } from './config'
import { setupRequestInterceptors, setupResponseInterceptors, setupRetryInterceptor } from './interceptors'
import type { RequestConfig } from './types'

function createRawInstance(config?: RequestConfig) {
  return axios.create({
    baseURL: BASE_URL,
    timeout: TIMEOUT,
    headers: DEFAULT_HEADERS,
    // 允许跨域请求携带 cookie
    withCredentials: false,
    // 合并自定义配置
    ...config
  })
}

/**
 * 创建 Axios 实例
 * @param config 自定义配置
 * @returns Axios 实例
 */
export function createAxiosInstance(config?: RequestConfig): AxiosInstance {
  // 创建 Axios 实例
  const instance = createRawInstance(config)

  // 设置拦截器
  setupRequestInterceptors(instance)
  setupResponseInterceptors(instance)
  setupRetryInterceptor(instance)

  return instance
}

export const rawInstance =  createRawInstance()
setupRequestInterceptors(rawInstance)

export default createAxiosInstance()
