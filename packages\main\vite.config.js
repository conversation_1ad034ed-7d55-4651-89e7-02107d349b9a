import { getNodeMajorVersion } from '@app/electron-versions'
import { spawn } from 'child_process'
import electronPath from 'electron'
import { defineConfig } from 'vite'
import { dirname, resolve } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

export default defineConfig({
  build: {
    ssr: true,
    sourcemap: 'inline',
    outDir: 'dist',
    assetsDir: '.',
    target: `node${getNodeMajorVersion()}`,
    lib: {
      entry: 'src/index.ts',
      formats: ['es'],
    },
    rollupOptions: {
      input: {
        index: resolve(__dirname, 'src/index.ts'),
        'upload-worker': resolve(__dirname, 'src/modules/crudable/upload-task/upload.worker.ts'),
      },
      output: {
        entryFileNames: '[name].js',
      },
    },
    emptyOutDir: true,
    reportCompressedSize: false,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@app/shared': resolve(__dirname, '../shared')
    }
  },
  plugins: [
    handleHotReload(),
  ],
})

/**
 * Implement Electron app reload when some file was changed
 * @return {import('vite').Plugin}
 */
function handleHotReload() {
  /** @type {ChildProcess} */
  let electronApp = null

  /** @type {import('vite').ViteDevServer|null} */
  let rendererWatchServer = null

  return {
    name: '@app/main-process-hot-reload',

    config(config, env) {
      if (env.mode !== 'development') {
        return
      }

      const rendererWatchServerProvider = config.plugins.find(p => p.name === '@app/renderer-watch-server-provider')
      if (!rendererWatchServerProvider) {
        throw new Error('Renderer watch server provider not found')
      }

      rendererWatchServer = rendererWatchServerProvider.api.provideRendererWatchServer()

      process.env.VITE_DEV_SERVER_URL = rendererWatchServer.resolvedUrls.local[0]

      return {
        build: {
          watch: {},
        },
      }
    },

    writeBundle() {
      if (process.env.NODE_ENV !== 'development') {
        return
      }

      /** Kill electron if a process already exists */
      if (electronApp !== null) {
        electronApp.removeListener('exit', process.exit)
        electronApp.kill('SIGINT')
        electronApp = null
      }

      /** Spawn a new electron process */
      electronApp = spawn(String(electronPath), ['--inspect', '.'], {
        stdio: 'inherit',
      })

      /** Stops the watch script when the application has been quit */
      electronApp.addListener('exit', process.exit)
    },
  }
}
