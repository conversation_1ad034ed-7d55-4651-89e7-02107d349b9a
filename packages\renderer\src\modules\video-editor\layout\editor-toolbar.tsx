import React, { useCallback } from 'react'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { DEFAULT_TRACKS, ZOOM_CONSTRAINTS } from '@/modules/video-editor/constants'
import { TooltipProvider } from '@/components/ui/tooltip'
import { Button, ButtonProps } from '@/components/ui/button'
import {
  DownloadIcon,
  FlipHorizontal2,
  Redo2,
  RotateCcw,
  RotateCwSquare,
  SquareSquare,
  Trash2,
  Undo2,
  ZoomIn,
  ZoomOut
} from 'lucide-react'
import { Slider } from '@/components/ui/slider'
import { WithTooltip } from '@/components/WithTooltip'
import { WithConfirm } from '@/components/WithConfirm'
import { DividerVerticalIcon } from '@radix-ui/react-icons'
import { safeParseJson } from '@app/shared/utils'
import { omit } from 'lodash'
import { useVideoHelper } from '@/modules/video-editor/hooks/helpers/useVideoHelper'
import { OverlayType } from '@clipnest/remotion-shared/types'

export const EditorToolbar: React.FC = () => {
  const {
    tracks,
    selectedOverlay,
    setTracksDirectly,
    deleteOverlay,
    resetOverlays,
    updateOverlay,
    updateTracks,
    history: { canUndo, canRedo, undo, redo }
  } = useEditorContext()

  const { flipCurrentOverlay } = useVideoHelper()

  const { zoomScale, setZoomScale } = useTimeline()

  const handleSliderChange = (value: number[]) => {
    setZoomScale(value[0] / 100)
  }

  const handleRotateCurrentOverlay = useCallback(() => {
    if (!selectedOverlay) return

    const newRotation = (selectedOverlay.rotation || 0) + 90
    return updateOverlay(selectedOverlay.id, {
      rotation: newRotation <= 180 ? newRotation : newRotation - 360,
    })
  }, [selectedOverlay])

  const handleDeleteCurrentOverlay = useCallback(() => {
    if (!selectedOverlay) return

    return deleteOverlay(selectedOverlay.id)
  }, [selectedOverlay, deleteOverlay])

  const handleZoomOut = () => setZoomScale(zoomScale - ZOOM_CONSTRAINTS.step)

  const handleZoomIn = () => setZoomScale(zoomScale + ZOOM_CONSTRAINTS.step)

  const handleResetZoom = () => setZoomScale(1)

  const toolbarButtonBaseProps: ButtonProps = {
    size: 'icon',
    variant: 'ghost',
    className: 'h-7 w-7 text-gray-700 dark:text-zinc-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80'
  }

  return (
    <TooltipProvider delayDuration={50}>
      <div className="px-4 py-2 flex justify-between">
        <div className="flex items-center">
          <WithConfirm
            asChild={true}
            title="警告"
            description="重置后所有更改将丢失，确定要重置吗？"
            confirmVariant="destructive"
            onConfirm={resetOverlays}
          >
            <Button {...toolbarButtonBaseProps}>
              <WithTooltip content="重置">
                <RotateCcw className="size-3.5 cursor-pointer text-red-400" />
              </WithTooltip>
            </Button>
          </WithConfirm>

          <WithTooltip content="撤销" shortcut={['Ctrl', 'Z']}>
            <Button
              onClick={undo}
              disabled={!canUndo}
              {...toolbarButtonBaseProps}
            >
              <Undo2 className="size-3.5" />
            </Button>
          </WithTooltip>

          <WithTooltip content="重做" shortcut={['Ctrl', 'Y']}>
            <Button
              onClick={redo}
              disabled={!canRedo}
              {...toolbarButtonBaseProps}
            >
              <Redo2 className="size-3.5" />
            </Button>
          </WithTooltip>

          <DividerVerticalIcon className="size-5 text-gray-700" />

          <WithTooltip content="顺时针旋转90°">
            <Button
              disabled={!selectedOverlay}
              onClick={handleRotateCurrentOverlay}
              {...toolbarButtonBaseProps}
            >
              <RotateCwSquare className="size-3.5" />
            </Button>
          </WithTooltip>

          <WithTooltip content="镜像">
            <Button
              disabled={!selectedOverlay}
              onClick={() => {
                if (selectedOverlay?.type === OverlayType.VIDEO) {
                  flipCurrentOverlay(selectedOverlay)
                }
              }}
              {...toolbarButtonBaseProps}
            >
              <FlipHorizontal2 className="size-3.5" />
            </Button>
          </WithTooltip>

          <DividerVerticalIcon className="size-5 text-gray-700" />

          <WithTooltip content="删除" shortcut="Del">
            <Button
              disabled={!selectedOverlay}
              onClick={handleDeleteCurrentOverlay}
              {...toolbarButtonBaseProps}
            >
              <Trash2 className="size-3.5 text-yellow-400" />
            </Button>
          </WithTooltip>
        </div>

        {/* Zoom Slider - Refined UI with Icons */}
        <div className="hidden sm:flex items-center gap-1 w-40">
          <WithTooltip content="从剪贴板导入数据">
            <DownloadIcon
              className="size-10"
              onClick={() => {
                navigator.clipboard.readText().then(r => setTracksDirectly(safeParseJson(r) || []))
              }}
            />
          </WithTooltip>

          <WithTooltip content="导出数据到剪切板" >
            <DownloadIcon
              className="size-10"
              onClick={() => {
                navigator.clipboard.writeText(
                  JSON.stringify(
                    tracks.map(t => ({
                      ...t,
                      overlays: t.overlays.map(o => omit(o, 'localSrc'))
                    }))
                  )
                )
              }}
            />
          </WithTooltip>

          <WithTooltip content="导入示例" >
            <DownloadIcon
              className="size-10"
              onClick={() => updateTracks(DEFAULT_TRACKS)}
            />
          </WithTooltip>

          <WithTooltip content="缩小" shortcut={['Ctrl', '-']}>
            <Button
              onClick={handleZoomOut}
              disabled={zoomScale <= ZOOM_CONSTRAINTS.min}
              {...toolbarButtonBaseProps}
            >
              <ZoomOut className="size-3.5" />
            </Button>
          </WithTooltip>
          <Slider
            value={[zoomScale * 100]}
            onValueChange={handleSliderChange}
            min={ZOOM_CONSTRAINTS.min * 100}
            max={ZOOM_CONSTRAINTS.max * 100}
            step={ZOOM_CONSTRAINTS.step * 100}
            className="w-full"
            aria-label="Timeline Zoom"
          />
          <WithTooltip content="放大" shortcut={['Ctrl', '+']}>
            <Button
              onClick={handleZoomIn}
              disabled={zoomScale >= ZOOM_CONSTRAINTS.max}
              {...toolbarButtonBaseProps}
            >
              <ZoomIn className="h-3.5 w-3.5" />
            </Button>
          </WithTooltip>

          <WithTooltip content="重置缩放" shortcut={['Ctrl', '=']}>
            <Button
              onClick={handleResetZoom}
              {...toolbarButtonBaseProps}
            >
              <SquareSquare className="h-3.5 w-3.5" />
            </Button>
          </WithTooltip>
        </div>
      </div>
    </TooltipProvider>
  )
}
