import { useSidebar } from '@/modules/video-editor/contexts'
import * as React from 'react'
import { useEffect, useMemo } from 'react'
import { getAllResourcePlugins } from '@/modules/video-editor/resource-plugin-system'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { SidebarMenuButton } from '@/components/ui/sidebar'
import { clsx } from 'clsx'

export function MaterialsSidebar() {
  const { activeResourcePanel, setActiveResourcePanel, setIsOpen } = useSidebar()

  const plugins = useMemo(() => getAllResourcePlugins(), [])

  useEffect(() => {
    setActiveResourcePanel(plugins[0].id as any)
  }, [])

  return (
    <div className="bg-background border-r px-2 py-3">
      <TooltipProvider delayDuration={0}>
        {plugins.map(item => (
          <Tooltip key={item.id}>
            <TooltipTrigger asChild>
              <SidebarMenuButton
                onClick={() => {
                  // 将字符串ID转换为对应的OverlayType枚举值
                  setActiveResourcePanel(item.id)
                  setIsOpen(true)
                }}
                size="lg"
                className={clsx(
                  'flex flex-col items-center gap-2 px-1.5 py-2',
                  activeResourcePanel === item.id
                    ? 'bg-primary/10 text-primary hover:bg-primary/10'
                    : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                )}
              >
                <item.icon
                  className="h-4 w-4 text-gray-700 dark:text-white font-light"
                  strokeWidth={1.25}
                />
                <span className="text-[8px] font-medium leading-none">
                  {item.title}
                </span>
              </SidebarMenuButton>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              className="border bg-background text-foreground"
            >
              {item.title}
            </TooltipContent>
          </Tooltip>
        ))}
      </TooltipProvider>
    </div>
  )
}
