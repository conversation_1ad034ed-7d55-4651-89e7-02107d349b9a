import React from 'react'
import { Camera } from 'lucide-react'
import { registerResourcePlugin } from '../registry'
import { ResourcePlugins } from '../types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/script.panel')
)

export default registerResourcePlugin({
  id: ResourcePlugins.SCRIPT,
  title: '脚本',
  icon: Camera,
  component: Panel,
  order: 0,
})
