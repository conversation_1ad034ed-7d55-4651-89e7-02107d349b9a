import React, { useState, useMemo } from 'react'
import { useParams } from 'react-router'
import { Trash, FolderInput } from 'lucide-react'
import { useQueryMediaRecycleList, useQueryDirRecycleList } from '@/hooks/queries/useQueryMaterial'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { MaterialResource } from '@/types/resources'
import { Button } from '@/components/ui/button'
import { useDeleteModal } from '@/components/modal/delete'
import MediaItem, { MediaAction, FolderAction } from '@/pages/Projects/material/components/MediaItem'
import { useProjectsContext } from '../../context'

const MaterialRecycle: React.FC = () => {
  const params = useParams()
  const { keyword } = useProjectsContext()
  const queryClient = useQueryClient()
  const { data: dirData, isLoading: isDirLoading } = useQueryDirRecycleList(
    {
      projectId: Number(params.projectId),
      keyWord: keyword,
    },
    { enabled: !!params.projectId },
  )
  const { data: mediaData, isLoading: isMediaLoading } = useQueryMediaRecycleList({
    projectId: Number(params.projectId),
    keyWord: keyword,
  })
  const [selectedMediaItems, setSelectedMediaItems] = useState<Set<string>>(new Set())
  const [selectedFolderItems, setSelectedFolderItems] = useState<Set<string>>(new Set())

  const deleteModal = useDeleteModal()

  const folderAsMediaItems: MaterialResource.Media[] = dirData?.map(folder => ({
    fileId: folder.folderId,
    fileName: folder.folderName,
    folderUuid: folder.parentId!,
    childrenFolder: 0,
    mediaNum: folder.imageCount + folder.videoCount,
    resType: 0, // 代表文件夹
    createTime: folder.createdAt,
    url: ''
  }))

  const toggleSelect = (fileId: string, isFolder: boolean) => {
    if (isFolder) {
      setSelectedFolderItems(prev => {
        const next = new Set(prev)
        if (next.has(fileId)) {
          next.delete(fileId)
        } else {
          next.add(fileId)
        }
        return next
      })
    } else {
      setSelectedMediaItems(prev => {
        const next = new Set(prev)
        if (next.has(fileId)) {
          next.delete(fileId)
        } else {
          next.add(fileId)
        }
        return next
      })
    }
  }

  const toggleSelectAll = () => {
    if (allSelected) {
      setSelectedMediaItems(new Set())
      setSelectedFolderItems(new Set())
    } else {
      const allMediaIds = mediaData?.pages.flatMap(page => page.list.map(media => media.fileId)) || []
      const allFolderIds = folderAsMediaItems.map(folder => folder.fileId)
      setSelectedMediaItems(new Set(allMediaIds))
      setSelectedFolderItems(new Set(allFolderIds))
    }
  }

  const allSelected = useMemo(() => {
    const allMediaIds = mediaData?.pages.flatMap(page => page.list.map(media => media.fileId)) || []
    const allFolderIds = folderAsMediaItems.map(folder => folder.fileId)

    const totalCount = allMediaIds.length + allFolderIds.length

    if (totalCount === 0) return false

    return allMediaIds.every(id => selectedMediaItems.has(id)) && allFolderIds.every(id => selectedFolderItems.has(id))
  }, [mediaData, folderAsMediaItems, selectedMediaItems, selectedFolderItems])

  const selectedCount = selectedMediaItems.size + selectedFolderItems.size

  const handleDelete = async () => {
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media.delete({ fileIds: Array.from(selectedMediaItems) })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_RECYCLR_LIST] })
    }

    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory.delete({
        folderIds: Array.from(selectedFolderItems),
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIR_RECYCLR_LIST] })
    }

    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const handleBack = async () => {
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media.back({ fileIds: Array.from(selectedMediaItems) })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_RECYCLR_LIST] })
    }

    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory.back({
        folderIds: Array.from(selectedFolderItems),
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIR_RECYCLR_LIST] })
    }

    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const folderActions: FolderAction[] = [
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '放回原处',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteModal({
          kind: '文件夹',
          name: label,
          danger: true,
          action: async () => {
            await ResourceModule.directory.back({ folderIds: [nodeId] })
            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.MATERIAL_DIR_RECYCLR_LIST],
            })
          },
        })
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '彻底删除',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteModal({
          kind: '文件夹',
          name: label,
          danger: true,
          action: async () => {
            await ResourceModule.directory.delete({ folderIds: [nodeId] })
            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.MATERIAL_DIR_RECYCLR_LIST],
            })
          },
        })
      },
    },
  ]
  const mediaActions: MediaAction[] = [
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '放回原处',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteModal({
          kind: '素材',
          name: label,
          danger: true,
          action: async () => {
            await ResourceModule.media.back({ fileIds: [nodeId] })
            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.MATERIAL_MEDIA_RECYCLR_LIST],
            })
          },
        })
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '彻底删除',
      onClick: (fileId, fileName, _folderUuid) => {
        deleteModal({
          kind: '素材',
          name: fileName,
          danger: true,
          action: async () => {
            await ResourceModule.media.delete({ fileIds: [fileId] })
            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.MATERIAL_MEDIA_RECYCLR_LIST],
            })
          },
        })
      },
    },
  ]
  const mediaCount = useMemo(() => {
    const mediaItemsCount =
      mediaData?.pages?.reduce((total, page) => {
        return total + (page.list?.length || 0)
      }, 0) ?? 0
    const folderCount = folderAsMediaItems.length
    return mediaItemsCount + folderCount
  }, [mediaData, folderAsMediaItems])

  return (
    <div className="p-4 pb-10 flex flex-col flex-1 h-full w-full overflow-auto">
      <div className="flex-1 h-full overflow-y-auto">
        <div className="flex items-center justify-between text-sm text-gray-600 pl-4">
          <div>文件在回收站保留15天，之后将被自动清除</div>
          <div className="flex justify-end items-center space-x-4">
            {selectedCount !== 0 && (
              <div>
                <Button
                  variant="default"
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-400 text-white"
                  onClick={handleBack}
                >
                  放回原处
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-400 text-white ml-2"
                  onClick={handleDelete}
                >
                  彻底删除
                </Button>
              </div>
            )}
            <label className="flex items-center cursor-pointer">
              <input type="checkbox" checked={allSelected} onChange={toggleSelectAll} className="mr-1" />
              全选
            </label>
            <span> | </span>
            <span>
              已选 {selectedCount}/{mediaCount}
            </span>
          </div>
        </div>

        {isDirLoading && isMediaLoading ? (
          <div className="flex items-center justify-center h-full">加载中...</div>
        ) : (
          <div className="flex flex-wrap gap-4 p-4">
            {folderAsMediaItems.map(folder => (
              <MediaItem
                isTrash={true}
                orientation={MaterialResource.MediaStyle.HORIZONTAL}
                media={folder}
                isSelected={selectedFolderItems.has(folder.fileId)}
                isFolder={true}
                actions={folderActions}
                onToggleSelect={fileId => toggleSelect(fileId, true)}
              />
            ))}

            {mediaData?.pages.map(page =>
              page.list.map(media => (
                <MediaItem
                  isTrash={true}
                  orientation={MaterialResource.MediaStyle.HORIZONTAL}
                  media={media}
                  isSelected={selectedMediaItems.has(media.fileId)}
                  isFolder={false}
                  actions={mediaActions}
                  onToggleSelect={fileId => toggleSelect(fileId, false)}
                />
              )),
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default MaterialRecycle
