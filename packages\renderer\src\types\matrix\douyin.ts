import { PaginationParams } from '@app/shared/types/database.types'

/**
   * 挂载模式枚举
   */
export enum MountType {
  /** 不挂载 */
  NONE = 0,
  /** 位置 */
  LOCATION = 1,
  /** 购物车 */
  CART = 2
}

/**
   * 发布模式枚举
   */
export enum PublishMode {
  /** 打卡 */
  CHECK_IN = 1,
  /** 带货 */
  GOODS = 2
}

/**
   * 发布设置枚举
   */
export enum Setting {
  /** 一个账号一个视频 */
  ONE_ACCOUNT_ONE_VIDEO = 1,
  /** 一个账号多个视频 */
  ONE_ACCOUNT_MULTIPLE_VIDEOS = 2
}

/**
   * 发布时间类型枚举
   */
export enum TimeType {
  /** 立即 */
  IMMEDIATE = 1,
  /** 定时 */
  SCHEDULED = 2,
  /** 循环 */
  LOOP = 3
}

/**
   * 定时模式间隔类型枚举
   */
export enum PeriodType {
  /** 单账号间隔 */
  SINGLE_ACCOUNT = 1,
  /** 多账号间隔 */
  MULTIPLE_ACCOUNTS = 2
}
export interface CreateMatrixParams {
  /**
     * 发布账号列表
     */
  accountIds: number[];
  /**
     * 挂车账号和商品信息
     */
  accountProducts: AccountProduct[];
  /**
     * 任务明细
     */
  detailDOS: DetailDOS[];
  /**
     * 营销目标
     */
  marketingTarget: string;
  /**
     * 挂载模式,0-不挂载，1-位置，2-购物车
     */
  mountType: number;
  /**
     * 计划名称
     */
  name: string;
  /**
     * 位置信息，挂载模式为1时必填
     */
  poi: PositionInfo;
  /**
     * 1-打卡，2-带货
     */
  publishMode: number;
  /**
     * 发布设置，1-一个账号一个视频，2-一个账号多个视频
     */
  setting: number;
  /**
     * 定时模式和循环模式时的具体配置
     */
  timeSetting: TimeSetting;
  /**
     * 发布时间设置，1-立即，2-定时，3-循环
     */
  timeType: number;
  /**
     * 标题列表
     */
  titles: string[];
  /**
     * 总账号数
     */
  totalAccount: number;
  /**
     * 总视频数
     */
  totalVideo: number;
  /**
     * 视频列表
     */
  videoList: VideoList[];
}

export interface AccountProduct {
  /**
     * 账号id
     */
  accountId: number;
  products: AccountProductProduct[];
}

export interface AccountProductProduct {
  title: string;
  url: string;
}

export interface DetailDOS {
  /**
     * 账号id
     */
  accountId: string;
  /**
     * 商品封面
     */
  cover: string;
  description: string;
  poiId: string;
  poiName: string;
  products: DetailDOProduct[];
  /**
     * 视频标题
     */
  title: string;
  /**
     * 视频链接
     */
  url: string;
}

export interface DetailDOProduct {
  title: string;
  url: string;
}

/**
  * 位置信息，挂载模式为1时必填
  */
export interface PositionInfo {
  poiId: string;
  poiName: string;
}

/**
 * 定时模式和循环模式时的具体配置
 */
export interface TimeSetting {
  /**
     * 循环模式的发布日期时间戳
     */
  loopDays: number[];
  /**
     * 循环发布间隔
     */
  loopPeriod: number;
  /**
     * 循环模式发布时间，格式为08:00:00
     */
  loopTime: string;
  /**
     * 循环模式，每天发布视频数
     */
  numEachDay: number;
  /**
     * 定时模式，间隔
     */
  period: number;
  /**
     * 定时模式间隔类型，1-单账号间隔，2-多账号间隔
     */
  periodType: number[];
  /**
     * 定时模式，发布时间
     */
  publishTime: number;
}

export interface VideoList {
  cover: string;
  url?: string;
  name?: string;
  orgCover?: string;
}

export interface CreateGroup {
  name: string;
  /**
     * 父分组id, 顶级分组不传
     */
  parentId?: number;
  tags: string;
}

export interface UpdateGroup {
  id: number;
  name: string;
  tags: string;
}

export interface GroupItem {
  accountNum: number;
  children: GroupItem[];
  id: number;
  name: string;
  parentId: number;
  tags: string;
}
export interface GroupAccountSearchParams extends PaginationParams {
  search?: string;
  groupId: number;
}

export interface AccountSearchParams extends PaginationParams {
  createTime?: number[];
  province?: string;
}

export interface Account {
  accessTokenStatus?: number;
  accessTokenStatusOrigin?: number;
  advertiserId?: number;
  avatar?: string;
  bindPhone?: null;
  channelType?: number;
  city?: string;
  commentNum?: null;
  createTime?: number;
  expiredTime?: number;
  fanNum?: null;
  groupList?: GroupList[];
  id: number;
  likeNum?: null;
  nickname?: string;
  openId?: string;
  orgAccountType?: number;
  orgNickname?: string;
  orgSessionId?: string;
  plusAccessTokenStatus?: number;
  poiCityCode?: number;
  poiId?: number;
  poiName?: string;
  province?: string;
  publishNum?: number;
  publishStatus?: number;
  remark?: string;
  sessionId?: string;
  shareNum?: null;
  subAccountId?: number;
}

export interface GroupList {
  accountId?: number;
  groupId?: number;
  name?: string;
}

export interface GroupAccount {
  avatar?: string;
  createTime?: number;
  id: number;
  nickname?: string;
}

export interface AccountPushDetailRequestParams extends PaginationParams {
  accountId?: number;
  createTime?: number[];
  planIds?: number[];
  status?: number;
  search?: string
}

export interface AccountPushDetail {
  accountId?: number;
  adId?: number;
  aspectRatio?: string;
  atUsers?: string;
  cancel?: boolean;
  challenges?: string;
  city?: string;
  commentNum?: null;
  createTime?: number;
  creativeId?: number;
  description?: string;
  diggNum?: null;
  error?: null;
  fileSource?: number;
  finalStatus?: number;
  id?: number;
  isPoiCommerce?: number;
  mentions?: string;
  name?: null;
  nickname?: string;
  planId?: number;
  playNum?: null;
  poiCityCode?: string;
  poiId?: string;
  poiName?: string;
  producerAdvertiserId?: number;
  producerName?: string;
  producerSubaccountId?: number;
  projectId?: number;
  province?: string;
  shareNum?: null;
  status?: number;
  textExtra?: string;
  title?: string;
  uploadStatus?: number;
  url?: string;
  videoCover?: string;
  videoDuration?: number;
  videoId?: number;
  videoName?: string;
}

export interface TimeRangeParams {
  startTime: number,
  endTime: number
}

export interface DyAccountOverview {
  authAccount: number;
  changeFans: number;
  commentNum: number;
  diggNum: number;
  fans: number;
  playNum: number;
  shareNum: number;
  totalVideo: number;
}

export interface DyPushPlan {
  /**
       * 关联商家
       */
  business?: null;
  channelType?: null;
  createTime?: number;
  detailVOList?: DyPushPlanDetail[];
  /**
       * 发布中数量
       */
  doing?: number;
  /**
       * 失败数量
       */
  fail?: number;
  /**
       * 计划id
       */
  id?: number;
  marketingTarget?: null;
  /**
       * 计划名
       */
  name?: string;
  /**
       * 发布时间
       */
  publishTime?: number;
  publishType?: null;
  /**
       * 发布状态, 0-待发布 1-发布中, 2-失败，3-成功，4-部分成功
       */
  status?: number;
  /**
       * 成功数量
       */
  success?: number;
  /**
       * 总账号数
       */
  totalAccount?: number;
  /**
       * 总视频数
       */
  totalVideo?: number;
  /**
       * 待发布数量
       */
  wait?: number;
}

export interface DyPushPlanDetail {
  accountId?: number;
  adId?: number;
  aspectRatio?: string;
  atUsers?: string;
  cancel?: boolean;
  challenges?: string;
  city?: string;
  createTime?: number;
  creativeId?: number;
  description?: string;
  error?: null;
  fileSource?: number;
  finalStatus?: null;
  id?: number;
  isPoiCommerce?: number;
  mentions?: string;
  name?: null;
  nickname?: null;
  planId?: number;
  poiCityCode?: string;
  poiId?: string;
  poiName?: string;
  producerAdvertiserId?: number;
  producerName?: string;
  producerSubaccountId?: number;
  projectId?: number;
  province?: string;
  status?: number;
  textExtra?: string;
  title?: string;
  uploadStatus?: number;
  url?: string;
  videoCover?: string;
  videoDuration?: number;
  videoId?: number;
  videoName?: string;
}

export interface DyPublishFormDetail {
  id: number
  name: string
  channelType: number
  publishType: number
  deliverType: number
  totalVideo: number
  totalAccount: number
  marketingTarget: string
  accountIds: number[]
  accountProducts: AccountProduct[]
  accountList: Account[]
  videoList: VideoDetailList[]
  titles: string[]
  businessId: any
  mountType: number
  poi: PositionInfo
  publishMode: number
  productLink: any
  setting: number
  timeType: number
  timeSetting: TimeSetting
  createTime: number
  updateTime: number
}

export interface VideoDetailList {
  url: string
  name: any
  videoId: any
  orgTitle: any
  duration: any
  size: any
  cover: string
  orgCover?: string
  title: any
}

